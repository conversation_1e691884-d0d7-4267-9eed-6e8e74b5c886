import { useEffect, useState } from "react";
import { useInstrumentDetail } from "./service/instrument-detail-service";

const getSelectedInstrumentId = () => {
  const instrumentId =
    window.EurolandAppContext?.command("instrument-selected")?.id ||
    window?.xprops?.data?.mainInstrumentId;
  return instrumentId ? parseInt(instrumentId) : undefined;
};

const PromodeInstrumentDetail = () => {
  const [selectedInstrumentId, setSelectedInstrumentId] = useState(
    getSelectedInstrumentId()
  );
  const { data, loading, error } = useInstrumentDetail(selectedInstrumentId);
  console.log({data, loading, error});
  useEffect(() => {
    const handleAuthChange = () => {
      setSelectedInstrumentId(getSelectedInstrumentId());
    };

    window.EurolandAppContext?.on("instrument-selected", handleAuthChange);

    return () => {
      window.EurolandAppContext?.off("instrument-selected", handleAuthChange);
    };
  }, []);



  return <div>PromodeInstrumentDetail {selectedInstrumentId}</div>;
};

export default PromodeInstrumentDetail;
