import { useState } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import "./ResizablePanels.scss";
import "./PromodeModal.scss";
import { appSettings } from "../../../../appSettings";
import { useSelectedTicker } from "../../../customHooks/useTickers";
import PromodeChart from "../PromodeChart/PromodeChart";
import PromodeInstrumentDetail from "../PromodeInstrumentDetail";

export default function PromodeModal() {
  const [activeTab, setActiveTab] = useState("tab1");
  const selectedInstrument = useSelectedTicker();

  const onClose = () => {
    window.xprops.close();
    console.log("onClose");
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleOverlayKeyDown = (e) => {
    if (e.key === "Escape") {
      onClose();
    }
  };

  return (
    <div
      className="promode-modal-overlay"
      onClick={handleOverlayClick}
      onKeyDown={handleOverlayKeyDown}
      role="button"
      aria-label="Close modal"
      tabIndex={0}
    >
      <div className="promode-modal">
        <div className="promode-modal__header">
          <h2>Pro Mode</h2>
          <button className="promode-modal__close-btn" onClick={onClose}>
            &times;
          </button>
        </div>

        <div className="promode-modal__body">
          <div className="promode-modal__tabs">
            <button
              className={`promode-modal__tab ${
                activeTab === "tab1" ? "active" : ""
              }`}
              onClick={() => setActiveTab("tab1")}
            >
              <i class="fs-market"></i>
            </button>
            <button
              className={`promode-modal__tab ${
                activeTab === "tab2" ? "active" : ""
              }`}
              onClick={() => setActiveTab("tab2")}
            >
              <i class="fs-alert"></i>
            </button>
          </div>
          <div className="promode-modal__content">
            <PanelGroup direction="horizontal">
              <Panel className="promode-panel" defaultSize={30}>
                {activeTab === "tab1" && (
                  <PanelGroup direction="vertical">
                    <Panel className="promode-panel" defaultSize={70}>
                    <div className="promode-panel__content">
                      <h5 className="promode-panel__title">Watchlist</h5>
                      <euroland-watch-list />
                    </div>
                    </Panel>
                    <PanelResizeHandle className="promode-resize-handle" />
                    <Panel className="promode-panel">
                    <div className="promode-panel__content">
                      <h5 className="promode-panel__title">News</h5>
                      {/* <euroland-news-widget /> */}
                      <PromodeInstrumentDetail />
                      </div>
                    </Panel>
                  </PanelGroup>
                )}
                {activeTab === "tab2" && (
                  <div className="promode-panel__content">
                    <euroland-share-alert
                    instrumentId={selectedInstrument.instrumentId}
                    name={selectedInstrument.shareName}
                    currentPrice={selectedInstrument.last} companyCode={appSettings.companyCode}
                    />
                  </div>
                )}
              </Panel>

              <PanelResizeHandle className="promode-resize-handle" />
              <Panel className="promode-panel">
                  <div className="promode-panel__content chart-panel">
                    <PromodeChart />
                  </div>
              </Panel>
            </PanelGroup>
          </div>
        </div>
      </div>
    </div>
  );
}
