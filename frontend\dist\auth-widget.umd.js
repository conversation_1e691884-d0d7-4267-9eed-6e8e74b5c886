!function(e){"function"==typeof define&&define.amd?define(e):e()}(function(){"use strict";var e,t,s,n,r,i,o,a,c,l,u,_,d={},h=[],p=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,g=Array.isArray;function f(e,t){for(var s in t)e[s]=t[s];return e}function w(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function m(t,s,n){var r,i,o,a={};for(o in s)"key"==o?r=s[o]:"ref"==o?i=s[o]:a[o]=s[o];if(arguments.length>2&&(a.children=arguments.length>3?e.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)void 0===a[o]&&(a[o]=t.defaultProps[o]);return y(t,a,r,i,null)}function y(e,n,r,i,o){var a={type:e,props:n,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==o?++s:o,__i:-1,__u:0};return null==o&&null!=t.vnode&&t.vnode(a),a}function v(e){return e.children}function S(e,t){this.props=e,this.context=t}function k(e,t){if(null==t)return e.__?k(e.__,e.__i+1):null;for(var s;t<e.__k.length;t++)if(null!=(s=e.__k[t])&&null!=s.__e)return s.__e;return"function"==typeof e.type?k(e):null}function b(e){var t,s;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(s=e.__k[t])&&null!=s.__e){e.__e=e.__c.base=s.__e;break}return b(e)}}function E(e){(!e.__d&&(e.__d=!0)&&n.push(e)&&!T.__r++||r!=t.debounceRendering)&&((r=t.debounceRendering)||i)(T)}function T(){for(var e,s,r,i,a,c,l,u=1;n.length;)n.length>u&&n.sort(o),e=n.shift(),u=n.length,e.__d&&(r=void 0,a=(i=(s=e).__v).__e,c=[],l=[],s.__P&&((r=f({},i)).__v=i.__v+1,t.vnode&&t.vnode(r),O(s.__P,r,i,s.__n,s.__P.namespaceURI,32&i.__u?[a]:null,c,null==a?k(i):a,!!(32&i.__u),l),r.__v=i.__v,r.__.__k[r.__i]=r,N(c,r,l),r.__e!=a&&b(r)));T.__r=0}function x(e,t,s,n,r,i,o,a,c,l,u){var _,p,f,w,m,S,b=n&&n.__k||h,E=t.length;for(c=function(e,t,s,n,r){var i,o,a,c,l,u=s.length,_=u,d=0;for(e.__k=new Array(r),i=0;i<r;i++)null!=(o=t[i])&&"boolean"!=typeof o&&"function"!=typeof o?(c=i+d,(o=e.__k[i]="string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?y(null,o,null,null,null):g(o)?y(v,{children:o},null,null,null):null==o.constructor&&o.__b>0?y(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o).__=e,o.__b=e.__b+1,a=null,-1!=(l=o.__i=R(o,s,c,_))&&(_--,(a=s[l])&&(a.__u|=2)),null==a||null==a.__v?(-1==l&&(r>u?d--:r<u&&d++),"function"!=typeof o.type&&(o.__u|=4)):l!=c&&(l==c-1?d--:l==c+1?d++:(l>c?d--:d++,o.__u|=4))):e.__k[i]=null;if(_)for(i=0;i<u;i++)null!=(a=s[i])&&!(2&a.__u)&&(a.__e==n&&(n=k(a)),L(a,a));return n}(s,t,b,c,E),_=0;_<E;_++)null!=(f=s.__k[_])&&(p=-1==f.__i?d:b[f.__i]||d,f.__i=_,S=O(e,f,p,r,i,o,a,c,l,u),w=f.__e,f.ref&&p.ref!=f.ref&&(p.ref&&H(p.ref,null,f),u.push(f.ref,f.__c||w,f)),null==m&&null!=w&&(m=w),4&f.__u||p.__k===f.__k?c=P(f,c,e):"function"==typeof f.type&&void 0!==S?c=S:w&&(c=w.nextSibling),f.__u&=-7);return s.__e=m,c}function P(e,t,s){var n,r;if("function"==typeof e.type){for(n=e.__k,r=0;n&&r<n.length;r++)n[r]&&(n[r].__=e,t=P(n[r],t,s));return t}e.__e!=t&&(t&&e.type&&!s.contains(t)&&(t=k(e)),s.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function C(e,t){return t=t||[],null==e||"boolean"==typeof e||(g(e)?e.some(function(e){C(e,t)}):t.push(e)),t}function R(e,t,s,n){var r,i,o=e.key,a=e.type,c=t[s];if(null===c&&null==e.key||c&&o==c.key&&a==c.type&&!(2&c.__u))return s;if(n>(null==c||2&c.__u?0:1))for(r=s-1,i=s+1;r>=0||i<t.length;){if(r>=0){if((c=t[r])&&!(2&c.__u)&&o==c.key&&a==c.type)return r;r--}if(i<t.length){if((c=t[i])&&!(2&c.__u)&&o==c.key&&a==c.type)return i;i++}}return-1}function I(e,t,s){"-"==t[0]?e.setProperty(t,null==s?"":s):e[t]=null==s?"":"number"!=typeof s||p.test(t)?s:s+"px"}function U(e,t,s,n,r){var i,o;e:if("style"==t)if("string"==typeof s)e.style.cssText=s;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)s&&t in s||I(e.style,t,"");if(s)for(t in s)n&&s[t]==n[t]||I(e.style,t,s[t])}else if("o"==t[0]&&"n"==t[1])i=t!=(t=t.replace(a,"$1")),o=t.toLowerCase(),t=o in e||"onFocusOut"==t||"onFocusIn"==t?o.slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=s,s?n?s.u=n.u:(s.u=c,e.addEventListener(t,i?u:l,i)):e.removeEventListener(t,i?u:l,i);else{if("http://www.w3.org/2000/svg"==r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==s?"":s;break e}catch(_){}"function"==typeof s||(null==s||!1===s&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==s?"":s))}}function A(e){return function(s){if(this.l){var n=this.l[s.type+e];if(null==s.t)s.t=c++;else if(s.t<n.u)return;return n(t.event?t.event(s):s)}}}function O(s,n,r,i,o,a,c,l,u,_){var h,p,m,y,b,E,T,P,C,R,I,A,O,N,H,L,M,j=n.type;if(null!=n.constructor)return null;128&r.__u&&(u=!!(32&r.__u),a=[l=n.__e=r.__e]),(h=t.__b)&&h(n);e:if("function"==typeof j)try{if(P=n.props,C="prototype"in j&&j.prototype.render,R=(h=j.contextType)&&i[h.__c],I=h?R?R.props.value:h.__:i,r.__c?T=(p=n.__c=r.__c).__=p.__E:(C?n.__c=p=new j(P,I):(n.__c=p=new S(P,I),p.constructor=j,p.render=$),R&&R.sub(p),p.props=P,p.state||(p.state={}),p.context=I,p.__n=i,m=p.__d=!0,p.__h=[],p._sb=[]),C&&null==p.__s&&(p.__s=p.state),C&&null!=j.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=f({},p.__s)),f(p.__s,j.getDerivedStateFromProps(P,p.__s))),y=p.props,b=p.state,p.__v=n,m)C&&null==j.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),C&&null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(C&&null==j.getDerivedStateFromProps&&P!==y&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(P,I),!p.__e&&null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(P,p.__s,I)||n.__v==r.__v){for(n.__v!=r.__v&&(p.props=P,p.state=p.__s,p.__d=!1),n.__e=r.__e,n.__k=r.__k,n.__k.some(function(e){e&&(e.__=n)}),A=0;A<p._sb.length;A++)p.__h.push(p._sb[A]);p._sb=[],p.__h.length&&c.push(p);break e}null!=p.componentWillUpdate&&p.componentWillUpdate(P,p.__s,I),C&&null!=p.componentDidUpdate&&p.__h.push(function(){p.componentDidUpdate(y,b,E)})}if(p.context=I,p.props=P,p.__P=s,p.__e=!1,O=t.__r,N=0,C){for(p.state=p.__s,p.__d=!1,O&&O(n),h=p.render(p.props,p.state,p.context),H=0;H<p._sb.length;H++)p.__h.push(p._sb[H]);p._sb=[]}else do{p.__d=!1,O&&O(n),h=p.render(p.props,p.state,p.context),p.state=p.__s}while(p.__d&&++N<25);p.state=p.__s,null!=p.getChildContext&&(i=f(f({},i),p.getChildContext())),C&&!m&&null!=p.getSnapshotBeforeUpdate&&(E=p.getSnapshotBeforeUpdate(y,b)),L=h,null!=h&&h.type===v&&null==h.key&&(L=q(h.props.children)),l=x(s,g(L)?L:[L],n,r,i,o,a,c,l,u,_),p.base=n.__e,n.__u&=-161,p.__h.length&&c.push(p),T&&(p.__E=p.__=null)}catch(W){if(n.__v=null,u||null!=a)if(W.then){for(n.__u|=u?160:128;l&&8==l.nodeType&&l.nextSibling;)l=l.nextSibling;a[a.indexOf(l)]=null,n.__e=l}else for(M=a.length;M--;)w(a[M]);else n.__e=r.__e,n.__k=r.__k;t.__e(W,n,r)}else null==a&&n.__v==r.__v?(n.__k=r.__k,n.__e=r.__e):l=n.__e=function(s,n,r,i,o,a,c,l,u){var _,h,p,f,m,y,v,S=r.props,b=n.props,E=n.type;if("svg"==E?o="http://www.w3.org/2000/svg":"math"==E?o="http://www.w3.org/1998/Math/MathML":o||(o="http://www.w3.org/1999/xhtml"),null!=a)for(_=0;_<a.length;_++)if((m=a[_])&&"setAttribute"in m==!!E&&(E?m.localName==E:3==m.nodeType)){s=m,a[_]=null;break}if(null==s){if(null==E)return document.createTextNode(b);s=document.createElementNS(o,E,b.is&&b),l&&(t.__m&&t.__m(n,a),l=!1),a=null}if(null==E)S===b||l&&s.data==b||(s.data=b);else{if(a=a&&e.call(s.childNodes),S=r.props||d,!l&&null!=a)for(S={},_=0;_<s.attributes.length;_++)S[(m=s.attributes[_]).name]=m.value;for(_ in S)if(m=S[_],"children"==_);else if("dangerouslySetInnerHTML"==_)p=m;else if(!(_ in b)){if("value"==_&&"defaultValue"in b||"checked"==_&&"defaultChecked"in b)continue;U(s,_,null,m,o)}for(_ in b)m=b[_],"children"==_?f=m:"dangerouslySetInnerHTML"==_?h=m:"value"==_?y=m:"checked"==_?v=m:l&&"function"!=typeof m||S[_]===m||U(s,_,m,S[_],o);if(h)l||p&&(h.__html==p.__html||h.__html==s.innerHTML)||(s.innerHTML=h.__html),n.__k=[];else if(p&&(s.innerHTML=""),x("template"==n.type?s.content:s,g(f)?f:[f],n,r,i,"foreignObject"==E?"http://www.w3.org/1999/xhtml":o,a,c,a?a[0]:r.__k&&k(r,0),l,u),null!=a)for(_=a.length;_--;)w(a[_]);l||(_="value","progress"==E&&null==y?s.removeAttribute("value"):null!=y&&(y!==s[_]||"progress"==E&&!y||"option"==E&&y!=S[_])&&U(s,_,y,S[_],o),_="checked",null!=v&&v!=s[_]&&U(s,_,v,S[_],o))}return s}(r.__e,n,r,i,o,a,c,u,_);return(h=t.diffed)&&h(n),128&n.__u?void 0:l}function N(e,s,n){for(var r=0;r<n.length;r++)H(n[r],n[++r],n[++r]);t.__c&&t.__c(s,e),e.some(function(s){try{e=s.__h,s.__h=[],e.some(function(e){e.call(s)})}catch(n){t.__e(n,s.__v)}})}function q(e){return"object"!=typeof e||null==e||e.__b&&e.__b>0?e:g(e)?e.map(q):f({},e)}function H(e,s,n){try{if("function"==typeof e){var r="function"==typeof e.__u;r&&e.__u(),r&&null==s||(e.__u=e(s))}else e.current=s}catch(i){t.__e(i,n)}}function L(e,s,n){var r,i;if(t.unmount&&t.unmount(e),(r=e.ref)&&(r.current&&r.current!=e.__e||H(r,null,s)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(o){t.__e(o,s)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&L(r[i],s,n||"function"!=typeof e.type);n||w(e.__e),e.__c=e.__=e.__e=void 0}function $(e,t,s){return this.constructor(e,s)}function M(s,n,r){var i,o,a;n==document&&(n=document.documentElement),t.__&&t.__(s,n),i=!1?null:n.__k,o=[],a=[],O(n,s=n.__k=m(v,null,[s]),i||d,d,n.namespaceURI,i?null:n.firstChild?e.call(n.childNodes):null,o,i?i.__e:n.firstChild,false,a),N(o,s,a)}e=h.slice,t={__e:function(e,t,s,n){for(var r,i,o;t=t.__;)if((r=t.__c)&&!r.__)try{if((i=r.constructor)&&null!=i.getDerivedStateFromError&&(r.setState(i.getDerivedStateFromError(e)),o=r.__d),null!=r.componentDidCatch&&(r.componentDidCatch(e,n||{}),o=r.__d),o)return r.__E=r}catch(a){e=a}throw e}},s=0,S.prototype.setState=function(e,t){var s;s=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=f({},this.state),"function"==typeof e&&(e=e(f({},s),this.props)),e&&f(s,e),null!=e&&this.__v&&(t&&this._sb.push(t),E(this))},S.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),E(this))},S.prototype.render=v,n=[],i="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,o=function(e,t){return e.__v.__b-t.__v.__b},T.__r=0,a=/(PointerCapture)$|Capture$/i,c=0,l=A(!1),u=A(!0),_=0;var j=0;function W(e,s,n,r,i,o){s||(s={});var a,c,l=s;if("ref"in l)for(c in l={},s)"ref"==c?a=s[c]:l[c]=s[c];var u={type:e,props:l,key:n,ref:a,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--j,__i:-1,__u:0,__source:i,__self:o};if("function"==typeof e&&(a=e.defaultProps))for(c in a)void 0===l[c]&&(l[c]=a[c]);return t.vnode&&t.vnode(u),u}var D,F,J,K,z=0,B=[],V=t,Q=V.__b,G=V.__r,Y=V.diffed,Z=V.__c,X=V.unmount,ee=V.__;function te(e,t){V.__h&&V.__h(F,e,z||t),z=0;var s=F.__H||(F.__H={__:[],__h:[]});return e>=s.__.length&&s.__.push({}),s.__[e]}function se(e,t,s){var n=te(D++,2);if(n.t=e,!n.__c&&(n.__=[s?s(t):he(void 0,t),function(e){var t=n.__N?n.__N[0]:n.__[0],s=n.t(t,e);t!==s&&(n.__N=[s,n.__[1]],n.__c.setState({}))}],n.__c=F,!F.__f)){var r=function(e,t,s){if(!n.__c.__H)return!0;var r=n.__c.__H.__.filter(function(e){return!!e.__c});if(r.every(function(e){return!e.__N}))return!i||i.call(this,e,t,s);var o=n.__c.props!==e;return r.forEach(function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}}),i&&i.call(this,e,t,s)||o};F.__f=!0;var i=F.shouldComponentUpdate,o=F.componentWillUpdate;F.componentWillUpdate=function(e,t,s){if(this.__e){var n=i;i=void 0,r(e,t,s),i=n}o&&o.call(this,e,t,s)},F.shouldComponentUpdate=r}return n.__N||n.__}function ne(e,t){var s=te(D++,3);!V.__s&&de(s.__H,t)&&(s.__=e,s.u=t,F.__H.__h.push(s))}function re(e){return z=5,ie(function(){return{current:e}},[])}function ie(e,t){var s=te(D++,7);return de(s.__H,t)&&(s.__=e(),s.__H=t,s.__h=e),s.__}function oe(e,t){return z=8,ie(function(){return e},t)}function ae(){for(var e;e=B.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(ue),e.__H.__h.forEach(_e),e.__H.__h=[]}catch(t){e.__H.__h=[],V.__e(t,e.__v)}}V.__b=function(e){F=null,Q&&Q(e)},V.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),ee&&ee(e,t)},V.__r=function(e){G&&G(e),D=0;var t=(F=e.__c).__H;t&&(J===F?(t.__h=[],F.__h=[],t.__.forEach(function(e){e.__N&&(e.__=e.__N),e.u=e.__N=void 0})):(t.__h.forEach(ue),t.__h.forEach(_e),t.__h=[],D=0)),J=F},V.diffed=function(e){Y&&Y(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==B.push(t)&&K===V.requestAnimationFrame||((K=V.requestAnimationFrame)||le)(ae)),t.__H.__.forEach(function(e){e.u&&(e.__H=e.u),e.u=void 0})),J=F=null},V.__c=function(e,t){t.some(function(e){try{e.__h.forEach(ue),e.__h=e.__h.filter(function(e){return!e.__||_e(e)})}catch(s){t.some(function(e){e.__h&&(e.__h=[])}),t=[],V.__e(s,e.__v)}}),Z&&Z(e,t)},V.unmount=function(e){X&&X(e);var t,s=e.__c;s&&s.__H&&(s.__H.__.forEach(function(e){try{ue(e)}catch(s){t=s}}),s.__H=void 0,t&&V.__e(t,s.__v))};var ce="function"==typeof requestAnimationFrame;function le(e){var t,s=function(){clearTimeout(n),ce&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(s,35);ce&&(t=requestAnimationFrame(s))}function ue(e){var t=F,s=e.__c;"function"==typeof s&&(e.__c=void 0,s()),F=t}function _e(e){var t=F;e.__c=e.__(),F=t}function de(e,t){return!e||e.length!==t.length||t.some(function(t,s){return t!==e[s]})}function he(e,t){return"function"==typeof t?t(e):t}function pe(e,t){for(var s in e)if("__source"!==s&&!(s in t))return!0;for(var n in t)if("__source"!==n&&e[n]!==t[n])return!0;return!1}function ge(e,t){this.props=e,this.context=t}(ge.prototype=new S).isPureReactComponent=!0,ge.prototype.shouldComponentUpdate=function(e,t){return pe(this.props,e)||pe(this.state,t)};var fe=t.__b;t.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),fe&&fe(e)};var we=t.__e;t.__e=function(e,t,s,n){if(e.then)for(var r,i=t;i=i.__;)if((r=i.__c)&&r.__c)return null==t.__e&&(t.__e=s.__e,t.__k=s.__k),r.__c(e,t);we(e,t,s,n)};var me=t.unmount;function ye(e,t,s){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach(function(e){"function"==typeof e.__c&&e.__c()}),e.__c.__H=null),null!=(e=function(e,t){for(var s in t)e[s]=t[s];return e}({},e)).__c&&(e.__c.__P===s&&(e.__c.__P=t),e.__c.__e=!0,e.__c=null),e.__k=e.__k&&e.__k.map(function(e){return ye(e,t,s)})),e}function ve(e,t,s){return e&&s&&(e.__v=null,e.__k=e.__k&&e.__k.map(function(e){return ve(e,t,s)}),e.__c&&e.__c.__P===t&&(e.__e&&s.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=s)),e}function Se(){this.__u=0,this.o=null,this.__b=null}function ke(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function be(){this.i=null,this.l=null}t.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),me&&me(e)},(Se.prototype=new S).__c=function(e,t){var s=t.__c,n=this;null==n.o&&(n.o=[]),n.o.push(s);var r=ke(n.__v),i=!1,o=function(){i||(i=!0,s.__R=null,r?r(a):a())};s.__R=o;var a=function(){if(! --n.__u){if(n.state.__a){var e=n.state.__a;n.__v.__k[0]=ve(e,e.__c.__P,e.__c.__O)}var t;for(n.setState({__a:n.__b=null});t=n.o.pop();)t.forceUpdate()}};n.__u++||32&t.__u||n.setState({__a:n.__b=n.__v.__k[0]}),e.then(o,o)},Se.prototype.componentWillUnmount=function(){this.o=[]},Se.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var s=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=ye(this.__b,s,n.__O=n.__P)}this.__b=null}var r=t.__a&&m(v,null,e.fallback);return r&&(r.__u&=-33),[m(v,null,t.__a?null:e.children),r]};var Ee=function(e,t,s){if(++s[1]===s[0]&&e.l.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.l.size))for(s=e.i;s;){for(;s.length>3;)s.pop()();if(s[1]<s[0])break;e.i=s=s[2]}};(be.prototype=new S).__a=function(e){var t=this,s=ke(t.__v),n=t.l.get(e);return n[0]++,function(r){var i=function(){t.props.revealOrder?(n.push(r),Ee(t,e,n)):r()};s?s(i):i()}},be.prototype.render=function(e){this.i=null,this.l=new Map;var t=C(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var s=t.length;s--;)this.l.set(t[s],this.i=[1,0,this.i]);return e.children},be.prototype.componentDidUpdate=be.prototype.componentDidMount=function(){var e=this;this.l.forEach(function(t,s){Ee(e,s,t)})};var Te="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,xe=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Pe=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Ce=/[A-Z0-9]/g,Re="undefined"!=typeof document,Ie=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};S.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(S.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var Ue=t.event;function Ae(){}function Oe(){return this.cancelBubble}function Ne(){return this.defaultPrevented}t.event=function(e){return Ue&&(e=Ue(e)),e.persist=Ae,e.isPropagationStopped=Oe,e.isDefaultPrevented=Ne,e.nativeEvent=e};var qe={enumerable:!1,configurable:!0,get:function(){return this.class}},He=t.vnode;t.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,s=e.type,n={},r=-1===s.indexOf("-");for(var i in t){var o=t[i];if(!("value"===i&&"defaultValue"in t&&null==o||Re&&"children"===i&&"noscript"===s||"class"===i||"className"===i)){var a=i.toLowerCase();"defaultValue"===i&&"value"in t&&null==t.value?i="value":"download"===i&&!0===o?o="":"translate"===a&&"no"===o?o=!1:"o"===a[0]&&"n"===a[1]?"ondoubleclick"===a?i="ondblclick":"onchange"!==a||"input"!==s&&"textarea"!==s||Ie(t.type)?"onfocus"===a?i="onfocusin":"onblur"===a?i="onfocusout":Pe.test(i)&&(i=a):a=i="oninput":r&&xe.test(i)?i=i.replace(Ce,"-$&").toLowerCase():null===o&&(o=void 0),"oninput"===a&&n[i=a]&&(i="oninputCapture"),n[i]=o}}"select"==s&&n.multiple&&Array.isArray(n.value)&&(n.value=C(t.children).forEach(function(e){e.props.selected=-1!=n.value.indexOf(e.props.value)})),"select"==s&&null!=n.defaultValue&&(n.value=C(t.children).forEach(function(e){e.props.selected=n.multiple?-1!=n.defaultValue.indexOf(e.props.value):n.defaultValue==e.props.value})),t.class&&!t.className?(n.class=t.class,Object.defineProperty(n,"className",qe)):(t.className&&!t.class||t.class&&t.className)&&(n.class=n.className=t.className),e.props=n}(e),e.$$typeof=Te,He&&He(e)};var Le=t.__r;t.__r=function(e){Le&&Le(e),e.__c};var $e=t.diffed;t.diffed=function(e){$e&&$e(e);var t=e.props,s=e.__e;null!=s&&"textarea"===e.type&&"value"in t&&t.value!==s.value&&(s.value=null==t.value?"":t.value)};class Me extends Error{}function je(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return function(e){return decodeURIComponent(atob(e).replace(/(.)/g,(e,t)=>{let s=t.charCodeAt(0).toString(16).toUpperCase();return s.length<2&&(s="0"+s),"%"+s}))}(t)}catch(s){return atob(t)}}Me.prototype.name="InvalidTokenError";var We,De,Fe,Je={debug:()=>{},info:()=>{},warn:()=>{},error:()=>{}},Ke=(e=>(e[e.NONE=0]="NONE",e[e.ERROR=1]="ERROR",e[e.WARN=2]="WARN",e[e.INFO=3]="INFO",e[e.DEBUG=4]="DEBUG",e))(Ke||{});(Fe=Ke||(Ke={})).reset=function(){We=3,De=Je},Fe.setLevel=function(e){if(!(0<=e&&e<=4))throw new Error("Invalid log level");We=e},Fe.setLogger=function(e){De=e};var ze=class e{constructor(e){this._name=e}debug(...t){We>=4&&De.debug(e._format(this._name,this._method),...t)}info(...t){We>=3&&De.info(e._format(this._name,this._method),...t)}warn(...t){We>=2&&De.warn(e._format(this._name,this._method),...t)}error(...t){We>=1&&De.error(e._format(this._name,this._method),...t)}throw(e){throw this.error(e),e}create(e){const t=Object.create(this);return t._method=e,t.debug("begin"),t}static createStatic(t,s){const n=new e(`${t}.${s}`);return n.debug("begin"),n}static _format(e,t){const s=`[${e}]`;return t?`${s} ${t}:`:s}static debug(t,...s){We>=4&&De.debug(e._format(t),...s)}static info(t,...s){We>=3&&De.info(e._format(t),...s)}static warn(t,...s){We>=2&&De.warn(e._format(t),...s)}static error(t,...s){We>=1&&De.error(e._format(t),...s)}};Ke.reset();var Be=class{static decode(e){try{return function(e,t){if("string"!=typeof e)throw new Me("Invalid token specified: must be a string");t||(t={});const s=!0===t.header?0:1,n=e.split(".")[s];if("string"!=typeof n)throw new Me(`Invalid token specified: missing part #${s+1}`);let r;try{r=je(n)}catch(i){throw new Me(`Invalid token specified: invalid base64 for part #${s+1} (${i.message})`)}try{return JSON.parse(r)}catch(i){throw new Me(`Invalid token specified: invalid json for part #${s+1} (${i.message})`)}}(e)}catch(t){throw ze.error("JwtUtils.decode",t),t}}static async generateSignedJwt(e,t,s){const n=`${Ge.encodeBase64Url((new TextEncoder).encode(JSON.stringify(e)))}.${Ge.encodeBase64Url((new TextEncoder).encode(JSON.stringify(t)))}`,r=await window.crypto.subtle.sign({name:"ECDSA",hash:{name:"SHA-256"}},s,(new TextEncoder).encode(n));return`${n}.${Ge.encodeBase64Url(new Uint8Array(r))}`}},Ve=e=>btoa([...new Uint8Array(e)].map(e=>String.fromCharCode(e)).join("")),Qe=class e{static _randomWord(){const e=new Uint32Array(1);return crypto.getRandomValues(e),e[0]}static generateUUIDv4(){return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,t=>(+t^e._randomWord()&15>>+t/4).toString(16)).replace(/-/g,"")}static generateCodeVerifier(){return e.generateUUIDv4()+e.generateUUIDv4()+e.generateUUIDv4()}static async generateCodeChallenge(e){if(!crypto.subtle)throw new Error("Crypto.subtle is available only in secure contexts (HTTPS).");try{const t=(new TextEncoder).encode(e),s=await crypto.subtle.digest("SHA-256",t);return Ve(s).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(t){throw ze.error("CryptoUtils.generateCodeChallenge",t),t}}static generateBasicAuth(e,t){const s=(new TextEncoder).encode([e,t].join(":"));return Ve(s)}static async hash(e,t){const s=(new TextEncoder).encode(t),n=await crypto.subtle.digest(e,s);return new Uint8Array(n)}static async customCalculateJwkThumbprint(t){let s;switch(t.kty){case"RSA":s={e:t.e,kty:t.kty,n:t.n};break;case"EC":s={crv:t.crv,kty:t.kty,x:t.x,y:t.y};break;case"OKP":s={crv:t.crv,kty:t.kty,x:t.x};break;case"oct":s={crv:t.k,kty:t.kty};break;default:throw new Error("Unknown jwk type")}const n=await e.hash("SHA-256",JSON.stringify(s));return e.encodeBase64Url(n)}static async generateDPoPProof({url:t,accessToken:s,httpMethod:n,keyPair:r,nonce:i}){let o,a;const c={jti:window.crypto.randomUUID(),htm:null!=n?n:"GET",htu:t,iat:Math.floor(Date.now()/1e3)};s&&(o=await e.hash("SHA-256",s),a=e.encodeBase64Url(o),c.ath=a),i&&(c.nonce=i);try{const e=await crypto.subtle.exportKey("jwk",r.publicKey),t={alg:"ES256",typ:"dpop+jwt",jwk:{crv:e.crv,kty:e.kty,x:e.x,y:e.y}};return await Be.generateSignedJwt(t,c,r.privateKey)}catch(l){throw l instanceof TypeError?new Error(`Error exporting dpop public key: ${l.message}`):l}}static async generateDPoPJkt(t){try{const s=await crypto.subtle.exportKey("jwk",t.publicKey);return await e.customCalculateJwkThumbprint(s)}catch(s){throw s instanceof TypeError?new Error(`Could not retrieve dpop keys from storage: ${s.message}`):s}}static async generateDPoPKeys(){return await window.crypto.subtle.generateKey({name:"ECDSA",namedCurve:"P-256"},!1,["sign","verify"])}};Qe.encodeBase64Url=e=>Ve(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_");var Ge=Qe,Ye=class{constructor(e){this._name=e,this._callbacks=[],this._logger=new ze(`Event('${this._name}')`)}addHandler(e){return this._callbacks.push(e),()=>this.removeHandler(e)}removeHandler(e){const t=this._callbacks.lastIndexOf(e);t>=0&&this._callbacks.splice(t,1)}async raise(...e){this._logger.debug("raise:",...e);for(const t of this._callbacks)await t(...e)}},Ze=class{static center({...e}){var t;return null==e.width&&(e.width=null!=(t=[800,720,600,480].find(e=>e<=window.outerWidth/1.618))?t:360),null!=e.left||(e.left=Math.max(0,Math.round(window.screenX+(window.outerWidth-e.width)/2))),null!=e.height&&(null!=e.top||(e.top=Math.max(0,Math.round(window.screenY+(window.outerHeight-e.height)/2)))),e}static serialize(e){return Object.entries(e).filter(([,e])=>null!=e).map(([e,t])=>`${e}=${"boolean"!=typeof t?t:t?"yes":"no"}`).join(",")}},Xe=class e extends Ye{constructor(){super(...arguments),this._logger=new ze(`Timer('${this._name}')`),this._timerHandle=null,this._expiration=0,this._callback=()=>{const t=this._expiration-e.getEpochTime();this._logger.debug("timer completes in",t),this._expiration<=e.getEpochTime()&&(this.cancel(),super.raise())}}static getEpochTime(){return Math.floor(Date.now()/1e3)}init(t){const s=this._logger.create("init");t=Math.max(Math.floor(t),1);const n=e.getEpochTime()+t;if(this.expiration===n&&this._timerHandle)return void s.debug("skipping since already initialized for expiration at",this.expiration);this.cancel(),s.debug("using duration",t),this._expiration=n;const r=Math.min(t,5);this._timerHandle=setInterval(this._callback,1e3*r)}get expiration(){return this._expiration}cancel(){this._logger.create("cancel"),this._timerHandle&&(clearInterval(this._timerHandle),this._timerHandle=null)}},et=class{static readParams(e,t="query"){if(!e)throw new TypeError("Invalid URL");const s=new URL(e,"http://127.0.0.1")["fragment"===t?"hash":"search"];return new URLSearchParams(s.slice(1))}},tt=";",st=class extends Error{constructor(e,t){var s,n,r;if(super(e.error_description||e.error||""),this.form=t,this.name="ErrorResponse",!e.error)throw ze.error("ErrorResponse","No error passed"),new Error("No error passed");this.error=e.error,this.error_description=null!=(s=e.error_description)?s:null,this.error_uri=null!=(n=e.error_uri)?n:null,this.state=e.userState,this.session_state=null!=(r=e.session_state)?r:null,this.url_state=e.url_state}},nt=class extends Error{constructor(e){super(e),this.name="ErrorTimeout"}},rt=class{constructor(e){this._logger=new ze("AccessTokenEvents"),this._expiringTimer=new Xe("Access token expiring"),this._expiredTimer=new Xe("Access token expired"),this._expiringNotificationTimeInSeconds=e.expiringNotificationTimeInSeconds}async load(e){const t=this._logger.create("load");if(e.access_token&&void 0!==e.expires_in){const s=e.expires_in;if(t.debug("access token present, remaining duration:",s),s>0){let e=s-this._expiringNotificationTimeInSeconds;e<=0&&(e=1),t.debug("registering expiring timer, raising in",e,"seconds"),this._expiringTimer.init(e)}else t.debug("canceling existing expiring timer because we're past expiration."),this._expiringTimer.cancel();const n=s+1;t.debug("registering expired timer, raising in",n,"seconds"),this._expiredTimer.init(n)}else this._expiringTimer.cancel(),this._expiredTimer.cancel()}async unload(){this._logger.debug("unload: canceling existing access token timers"),this._expiringTimer.cancel(),this._expiredTimer.cancel()}addAccessTokenExpiring(e){return this._expiringTimer.addHandler(e)}removeAccessTokenExpiring(e){this._expiringTimer.removeHandler(e)}addAccessTokenExpired(e){return this._expiredTimer.addHandler(e)}removeAccessTokenExpired(e){this._expiredTimer.removeHandler(e)}},it=class{constructor(e,t,s,n,r){this._callback=e,this._client_id=t,this._intervalInSeconds=n,this._stopOnError=r,this._logger=new ze("CheckSessionIFrame"),this._timer=null,this._session_state=null,this._message=e=>{e.origin===this._frame_origin&&e.source===this._frame.contentWindow&&("error"===e.data?(this._logger.error("error message from check session op iframe"),this._stopOnError&&this.stop()):"changed"===e.data?(this._logger.debug("changed message from check session op iframe"),this.stop(),this._callback()):this._logger.debug(e.data+" message from check session op iframe"))};const i=new URL(s);this._frame_origin=i.origin,this._frame=window.document.createElement("iframe"),this._frame.style.visibility="hidden",this._frame.style.position="fixed",this._frame.style.left="-1000px",this._frame.style.top="0",this._frame.width="0",this._frame.height="0",this._frame.src=i.href}load(){return new Promise(e=>{this._frame.onload=()=>{e()},window.document.body.appendChild(this._frame),window.addEventListener("message",this._message,!1)})}start(e){if(this._session_state===e)return;this._logger.create("start"),this.stop(),this._session_state=e;const t=()=>{this._frame.contentWindow&&this._session_state&&this._frame.contentWindow.postMessage(this._client_id+" "+this._session_state,this._frame_origin)};t(),this._timer=setInterval(t,1e3*this._intervalInSeconds)}stop(){this._logger.create("stop"),this._session_state=null,this._timer&&(clearInterval(this._timer),this._timer=null)}},ot=class{constructor(){this._logger=new ze("InMemoryWebStorage"),this._data={}}clear(){this._logger.create("clear"),this._data={}}getItem(e){return this._logger.create(`getItem('${e}')`),this._data[e]}setItem(e,t){this._logger.create(`setItem('${e}')`),this._data[e]=t}removeItem(e){this._logger.create(`removeItem('${e}')`),delete this._data[e]}get length(){return Object.getOwnPropertyNames(this._data).length}key(e){return Object.getOwnPropertyNames(this._data)[e]}},at=class extends Error{constructor(e,t){super(t),this.name="ErrorDPoPNonce",this.nonce=e}},ct=class{constructor(e=[],t=null,s={}){this._jwtHandler=t,this._extraHeaders=s,this._logger=new ze("JsonService"),this._contentTypes=[],this._contentTypes.push(...e,"application/json"),t&&this._contentTypes.push("application/jwt")}async fetchWithTimeout(e,t={}){const{timeoutInSeconds:s,...n}=t;if(!s)return await fetch(e,n);const r=new AbortController,i=setTimeout(()=>r.abort(),1e3*s);try{return await fetch(e,{...t,signal:r.signal})}catch(o){if(o instanceof DOMException&&"AbortError"===o.name)throw new nt("Network timed out");throw o}finally{clearTimeout(i)}}async getJson(e,{token:t,credentials:s,timeoutInSeconds:n}={}){const r=this._logger.create("getJson"),i={Accept:this._contentTypes.join(", ")};let o;t&&(r.debug("token passed, setting Authorization header"),i.Authorization="Bearer "+t),this._appendExtraHeaders(i);try{r.debug("url:",e),o=await this.fetchWithTimeout(e,{method:"GET",headers:i,timeoutInSeconds:n,credentials:s})}catch(l){throw r.error("Network Error"),l}r.debug("HTTP response received, status",o.status);const a=o.headers.get("Content-Type");if(a&&!this._contentTypes.find(e=>a.startsWith(e))&&r.throw(new Error(`Invalid response Content-Type: ${null!=a?a:"undefined"}, from URL: ${e}`)),o.ok&&this._jwtHandler&&(null==a?void 0:a.startsWith("application/jwt")))return await this._jwtHandler(await o.text());let c;try{c=await o.json()}catch(l){if(r.error("Error parsing JSON response",l),o.ok)throw l;throw new Error(`${o.statusText} (${o.status})`)}if(!o.ok){if(r.error("Error from server:",c),c.error)throw new st(c);throw new Error(`${o.statusText} (${o.status}): ${JSON.stringify(c)}`)}return c}async postForm(e,{body:t,basicAuth:s,timeoutInSeconds:n,initCredentials:r,extraHeaders:i}){const o=this._logger.create("postForm"),a={Accept:this._contentTypes.join(", "),"Content-Type":"application/x-www-form-urlencoded",...i};let c;void 0!==s&&(a.Authorization="Basic "+s),this._appendExtraHeaders(a);try{o.debug("url:",e),c=await this.fetchWithTimeout(e,{method:"POST",headers:a,body:t,timeoutInSeconds:n,credentials:r})}catch(d){throw o.error("Network error"),d}o.debug("HTTP response received, status",c.status);const l=c.headers.get("Content-Type");if(l&&!this._contentTypes.find(e=>l.startsWith(e)))throw new Error(`Invalid response Content-Type: ${null!=l?l:"undefined"}, from URL: ${e}`);const u=await c.text();let _={};if(u)try{_=JSON.parse(u)}catch(d){if(o.error("Error parsing JSON response",d),c.ok)throw d;throw new Error(`${c.statusText} (${c.status})`)}if(!c.ok){if(o.error("Error from server:",_),c.headers.has("dpop-nonce")){const e=c.headers.get("dpop-nonce");throw new at(e,`${JSON.stringify(_)}`)}if(_.error)throw new st(_,t);throw new Error(`${c.statusText} (${c.status}): ${JSON.stringify(_)}`)}return _}_appendExtraHeaders(e){const t=this._logger.create("appendExtraHeaders"),s=Object.keys(this._extraHeaders),n=["accept","content-type"],r=["authorization"];0!==s.length&&s.forEach(s=>{if(n.includes(s.toLocaleLowerCase()))return void t.warn("Protected header could not be set",s,n);if(r.includes(s.toLocaleLowerCase())&&Object.keys(e).includes(s))return void t.warn("Header could not be overridden",s,r);const i="function"==typeof this._extraHeaders[s]?this._extraHeaders[s]():this._extraHeaders[s];i&&""!==i&&(e[s]=i)})}},lt=class{constructor(e){this._settings=e,this._logger=new ze("MetadataService"),this._signingKeys=null,this._metadata=null,this._metadataUrl=this._settings.metadataUrl,this._jsonService=new ct(["application/jwk-set+json"],null,this._settings.extraHeaders),this._settings.signingKeys&&(this._logger.debug("using signingKeys from settings"),this._signingKeys=this._settings.signingKeys),this._settings.metadata&&(this._logger.debug("using metadata from settings"),this._metadata=this._settings.metadata),this._settings.fetchRequestCredentials&&(this._logger.debug("using fetchRequestCredentials from settings"),this._fetchRequestCredentials=this._settings.fetchRequestCredentials)}resetSigningKeys(){this._signingKeys=null}async getMetadata(){const e=this._logger.create("getMetadata");if(this._metadata)return e.debug("using cached values"),this._metadata;if(!this._metadataUrl)throw e.throw(new Error("No authority or metadataUrl configured on settings")),null;e.debug("getting metadata from",this._metadataUrl);const t=await this._jsonService.getJson(this._metadataUrl,{credentials:this._fetchRequestCredentials,timeoutInSeconds:this._settings.requestTimeoutInSeconds});return e.debug("merging remote JSON with seed metadata"),this._metadata=Object.assign({},t,this._settings.metadataSeed),this._metadata}getIssuer(){return this._getMetadataProperty("issuer")}getAuthorizationEndpoint(){return this._getMetadataProperty("authorization_endpoint")}getUserInfoEndpoint(){return this._getMetadataProperty("userinfo_endpoint")}getTokenEndpoint(e=!0){return this._getMetadataProperty("token_endpoint",e)}getCheckSessionIframe(){return this._getMetadataProperty("check_session_iframe",!0)}getEndSessionEndpoint(){return this._getMetadataProperty("end_session_endpoint",!0)}getRevocationEndpoint(e=!0){return this._getMetadataProperty("revocation_endpoint",e)}getKeysEndpoint(e=!0){return this._getMetadataProperty("jwks_uri",e)}async _getMetadataProperty(e,t=!1){const s=this._logger.create(`_getMetadataProperty('${e}')`),n=await this.getMetadata();if(s.debug("resolved"),void 0===n[e]){if(!0===t)return void s.warn("Metadata does not contain optional property");s.throw(new Error("Metadata does not contain property "+e))}return n[e]}async getSigningKeys(){const e=this._logger.create("getSigningKeys");if(this._signingKeys)return e.debug("returning signingKeys from cache"),this._signingKeys;const t=await this.getKeysEndpoint(!1);e.debug("got jwks_uri",t);const s=await this._jsonService.getJson(t,{timeoutInSeconds:this._settings.requestTimeoutInSeconds});if(e.debug("got key set",s),!Array.isArray(s.keys))throw e.throw(new Error("Missing keys on keyset")),null;return this._signingKeys=s.keys,this._signingKeys}},ut=class{constructor({prefix:e="oidc.",store:t=localStorage}={}){this._logger=new ze("WebStorageStateStore"),this._store=t,this._prefix=e}async set(e,t){this._logger.create(`set('${e}')`),e=this._prefix+e,await this._store.setItem(e,t)}async get(e){this._logger.create(`get('${e}')`),e=this._prefix+e;return await this._store.getItem(e)}async remove(e){this._logger.create(`remove('${e}')`),e=this._prefix+e;const t=await this._store.getItem(e);return await this._store.removeItem(e),t}async getAllKeys(){this._logger.create("getAllKeys");const e=await this._store.length,t=[];for(let s=0;s<e;s++){const e=await this._store.key(s);e&&0===e.indexOf(this._prefix)&&t.push(e.substr(this._prefix.length))}return t}},_t=class{constructor({authority:e,metadataUrl:t,metadata:s,signingKeys:n,metadataSeed:r,client_id:i,client_secret:o,response_type:a="code",scope:c="openid",redirect_uri:l,post_logout_redirect_uri:u,client_authentication:_="client_secret_post",prompt:d,display:h,max_age:p,ui_locales:g,acr_values:f,resource:w,response_mode:m,filterProtocolClaims:y=!0,loadUserInfo:v=!1,requestTimeoutInSeconds:S,staleStateAgeInSeconds:k=900,mergeClaimsStrategy:b={array:"replace"},disablePKCE:E=!1,stateStore:T,revokeTokenAdditionalContentTypes:x,fetchRequestCredentials:P,refreshTokenAllowedScope:C,extraQueryParams:R={},extraTokenParams:I={},extraHeaders:U={},dpop:A,omitScopeWhenRequesting:O=!1}){var N;if(this.authority=e,t?this.metadataUrl=t:(this.metadataUrl=e,e&&(this.metadataUrl.endsWith("/")||(this.metadataUrl+="/"),this.metadataUrl+=".well-known/openid-configuration")),this.metadata=s,this.metadataSeed=r,this.signingKeys=n,this.client_id=i,this.client_secret=o,this.response_type=a,this.scope=c,this.redirect_uri=l,this.post_logout_redirect_uri=u,this.client_authentication=_,this.prompt=d,this.display=h,this.max_age=p,this.ui_locales=g,this.acr_values=f,this.resource=w,this.response_mode=m,this.filterProtocolClaims=null==y||y,this.loadUserInfo=!!v,this.staleStateAgeInSeconds=k,this.mergeClaimsStrategy=b,this.omitScopeWhenRequesting=O,this.disablePKCE=!!E,this.revokeTokenAdditionalContentTypes=x,this.fetchRequestCredentials=P||"same-origin",this.requestTimeoutInSeconds=S,T)this.stateStore=T;else{const e="undefined"!=typeof window?window.localStorage:new ot;this.stateStore=new ut({store:e})}if(this.refreshTokenAllowedScope=C,this.extraQueryParams=R,this.extraTokenParams=I,this.extraHeaders=U,this.dpop=A,this.dpop&&!(null==(N=this.dpop)?void 0:N.store))throw new Error("A DPoPStore is required when dpop is enabled")}},dt=class{constructor(e,t){this._settings=e,this._metadataService=t,this._logger=new ze("UserInfoService"),this._getClaimsFromJwt=async e=>{const t=this._logger.create("_getClaimsFromJwt");try{const s=Be.decode(e);return t.debug("JWT decoding successful"),s}catch(s){throw t.error("Error parsing JWT response"),s}},this._jsonService=new ct(void 0,this._getClaimsFromJwt,this._settings.extraHeaders)}async getClaims(e){const t=this._logger.create("getClaims");e||this._logger.throw(new Error("No token passed"));const s=await this._metadataService.getUserInfoEndpoint();t.debug("got userinfo url",s);const n=await this._jsonService.getJson(s,{token:e,credentials:this._settings.fetchRequestCredentials,timeoutInSeconds:this._settings.requestTimeoutInSeconds});return t.debug("got claims",n),n}},ht=class{constructor(e,t){this._settings=e,this._metadataService=t,this._logger=new ze("TokenClient"),this._jsonService=new ct(this._settings.revokeTokenAdditionalContentTypes,null,this._settings.extraHeaders)}async exchangeCode({grant_type:e="authorization_code",redirect_uri:t=this._settings.redirect_uri,client_id:s=this._settings.client_id,client_secret:n=this._settings.client_secret,extraHeaders:r,...i}){const o=this._logger.create("exchangeCode");s||o.throw(new Error("A client_id is required")),t||o.throw(new Error("A redirect_uri is required")),i.code||o.throw(new Error("A code is required"));const a=new URLSearchParams({grant_type:e,redirect_uri:t});for(const[_,d]of Object.entries(i))null!=d&&a.set(_,d);let c;switch(this._settings.client_authentication){case"client_secret_basic":if(null==n)throw o.throw(new Error("A client_secret is required")),null;c=Ge.generateBasicAuth(s,n);break;case"client_secret_post":a.append("client_id",s),n&&a.append("client_secret",n)}const l=await this._metadataService.getTokenEndpoint(!1);o.debug("got token endpoint");const u=await this._jsonService.postForm(l,{body:a,basicAuth:c,timeoutInSeconds:this._settings.requestTimeoutInSeconds,initCredentials:this._settings.fetchRequestCredentials,extraHeaders:r});return o.debug("got response"),u}async exchangeCredentials({grant_type:e="password",client_id:t=this._settings.client_id,client_secret:s=this._settings.client_secret,scope:n=this._settings.scope,...r}){const i=this._logger.create("exchangeCredentials");t||i.throw(new Error("A client_id is required"));const o=new URLSearchParams({grant_type:e});this._settings.omitScopeWhenRequesting||o.set("scope",n);for(const[u,_]of Object.entries(r))null!=_&&o.set(u,_);let a;switch(this._settings.client_authentication){case"client_secret_basic":if(null==s)throw i.throw(new Error("A client_secret is required")),null;a=Ge.generateBasicAuth(t,s);break;case"client_secret_post":o.append("client_id",t),s&&o.append("client_secret",s)}const c=await this._metadataService.getTokenEndpoint(!1);i.debug("got token endpoint");const l=await this._jsonService.postForm(c,{body:o,basicAuth:a,timeoutInSeconds:this._settings.requestTimeoutInSeconds,initCredentials:this._settings.fetchRequestCredentials});return i.debug("got response"),l}async exchangeRefreshToken({grant_type:e="refresh_token",client_id:t=this._settings.client_id,client_secret:s=this._settings.client_secret,timeoutInSeconds:n,extraHeaders:r,...i}){const o=this._logger.create("exchangeRefreshToken");t||o.throw(new Error("A client_id is required")),i.refresh_token||o.throw(new Error("A refresh_token is required"));const a=new URLSearchParams({grant_type:e});for(const[_,d]of Object.entries(i))Array.isArray(d)?d.forEach(e=>a.append(_,e)):null!=d&&a.set(_,d);let c;switch(this._settings.client_authentication){case"client_secret_basic":if(null==s)throw o.throw(new Error("A client_secret is required")),null;c=Ge.generateBasicAuth(t,s);break;case"client_secret_post":a.append("client_id",t),s&&a.append("client_secret",s)}const l=await this._metadataService.getTokenEndpoint(!1);o.debug("got token endpoint");const u=await this._jsonService.postForm(l,{body:a,basicAuth:c,timeoutInSeconds:n,initCredentials:this._settings.fetchRequestCredentials,extraHeaders:r});return o.debug("got response"),u}async revoke(e){var t;const s=this._logger.create("revoke");e.token||s.throw(new Error("A token is required"));const n=await this._metadataService.getRevocationEndpoint(!1);s.debug(`got revocation endpoint, revoking ${null!=(t=e.token_type_hint)?t:"default token type"}`);const r=new URLSearchParams;for(const[i,o]of Object.entries(e))null!=o&&r.set(i,o);r.set("client_id",this._settings.client_id),this._settings.client_secret&&r.set("client_secret",this._settings.client_secret),await this._jsonService.postForm(n,{body:r,timeoutInSeconds:this._settings.requestTimeoutInSeconds}),s.debug("got response")}},pt=class{constructor(e,t,s){this._settings=e,this._metadataService=t,this._claimsService=s,this._logger=new ze("ResponseValidator"),this._userInfoService=new dt(this._settings,this._metadataService),this._tokenClient=new ht(this._settings,this._metadataService)}async validateSigninResponse(e,t,s){const n=this._logger.create("validateSigninResponse");this._processSigninState(e,t),n.debug("state processed"),await this._processCode(e,t,s),n.debug("code processed"),e.isOpenId&&this._validateIdTokenAttributes(e),n.debug("tokens validated"),await this._processClaims(e,null==t?void 0:t.skipUserInfo,e.isOpenId),n.debug("claims processed")}async validateCredentialsResponse(e,t){const s=this._logger.create("validateCredentialsResponse"),n=e.isOpenId&&!!e.id_token;n&&this._validateIdTokenAttributes(e),s.debug("tokens validated"),await this._processClaims(e,t,n),s.debug("claims processed")}async validateRefreshResponse(e,t){const s=this._logger.create("validateRefreshResponse");e.userState=t.data,null!=e.session_state||(e.session_state=t.session_state),null!=e.scope||(e.scope=t.scope),e.isOpenId&&e.id_token&&(this._validateIdTokenAttributes(e,t.id_token),s.debug("ID Token validated")),e.id_token||(e.id_token=t.id_token,e.profile=t.profile);const n=e.isOpenId&&!!e.id_token;await this._processClaims(e,!1,n),s.debug("claims processed")}validateSignoutResponse(e,t){const s=this._logger.create("validateSignoutResponse");if(t.id!==e.state&&s.throw(new Error("State does not match")),s.debug("state validated"),e.userState=t.data,e.error)throw s.warn("Response was error",e.error),new st(e)}_processSigninState(e,t){const s=this._logger.create("_processSigninState");if(t.id!==e.state&&s.throw(new Error("State does not match")),t.client_id||s.throw(new Error("No client_id on state")),t.authority||s.throw(new Error("No authority on state")),this._settings.authority!==t.authority&&s.throw(new Error("authority mismatch on settings vs. signin state")),this._settings.client_id&&this._settings.client_id!==t.client_id&&s.throw(new Error("client_id mismatch on settings vs. signin state")),s.debug("state validated"),e.userState=t.data,e.url_state=t.url_state,null!=e.scope||(e.scope=t.scope),e.error)throw s.warn("Response was error",e.error),new st(e);t.code_verifier&&!e.code&&s.throw(new Error("Expected code in response"))}async _processClaims(e,t=!1,s=!0){const n=this._logger.create("_processClaims");if(e.profile=this._claimsService.filterProtocolClaims(e.profile),t||!this._settings.loadUserInfo||!e.access_token)return void n.debug("not loading user info");n.debug("loading user info");const r=await this._userInfoService.getClaims(e.access_token);n.debug("user info claims received from user info endpoint"),s&&r.sub!==e.profile.sub&&n.throw(new Error("subject from UserInfo response does not match subject in ID Token")),e.profile=this._claimsService.mergeClaims(e.profile,this._claimsService.filterProtocolClaims(r)),n.debug("user info claims received, updated profile:",e.profile)}async _processCode(e,t,s){const n=this._logger.create("_processCode");if(e.code){n.debug("Validating code");const r=await this._tokenClient.exchangeCode({client_id:t.client_id,client_secret:t.client_secret,code:e.code,redirect_uri:t.redirect_uri,code_verifier:t.code_verifier,extraHeaders:s,...t.extraTokenParams});Object.assign(e,r)}else n.debug("No code to process")}_validateIdTokenAttributes(e,t){var s;const n=this._logger.create("_validateIdTokenAttributes");n.debug("decoding ID Token JWT");const r=Be.decode(null!=(s=e.id_token)?s:"");if(r.sub||n.throw(new Error("ID Token is missing a subject claim")),t){const e=Be.decode(t);r.sub!==e.sub&&n.throw(new Error("sub in id_token does not match current sub")),r.auth_time&&r.auth_time!==e.auth_time&&n.throw(new Error("auth_time in id_token does not match original auth_time")),r.azp&&r.azp!==e.azp&&n.throw(new Error("azp in id_token does not match original azp")),!r.azp&&e.azp&&n.throw(new Error("azp not in id_token, but present in original id_token"))}e.profile=r}},gt=class e{constructor(e){this.id=e.id||Ge.generateUUIDv4(),this.data=e.data,e.created&&e.created>0?this.created=e.created:this.created=Xe.getEpochTime(),this.request_type=e.request_type,this.url_state=e.url_state}toStorageString(){return new ze("State").create("toStorageString"),JSON.stringify({id:this.id,data:this.data,created:this.created,request_type:this.request_type,url_state:this.url_state})}static fromStorageString(t){return ze.createStatic("State","fromStorageString"),Promise.resolve(new e(JSON.parse(t)))}static async clearStaleState(t,s){const n=ze.createStatic("State","clearStaleState"),r=Xe.getEpochTime()-s,i=await t.getAllKeys();n.debug("got keys",i);for(let a=0;a<i.length;a++){const s=i[a],c=await t.get(s);let l=!1;if(c)try{const t=await e.fromStorageString(c);n.debug("got item from key:",s,t.created),t.created<=r&&(l=!0)}catch(o){n.error("Error parsing state for key:",s,o),l=!0}else n.debug("no item in storage for key:",s),l=!0;l&&(n.debug("removed item for key:",s),t.remove(s))}}},ft=class e extends gt{constructor(e){super(e),this.code_verifier=e.code_verifier,this.code_challenge=e.code_challenge,this.authority=e.authority,this.client_id=e.client_id,this.redirect_uri=e.redirect_uri,this.scope=e.scope,this.client_secret=e.client_secret,this.extraTokenParams=e.extraTokenParams,this.response_mode=e.response_mode,this.skipUserInfo=e.skipUserInfo}static async create(t){const s=!0===t.code_verifier?Ge.generateCodeVerifier():t.code_verifier||void 0,n=s?await Ge.generateCodeChallenge(s):void 0;return new e({...t,code_verifier:s,code_challenge:n})}toStorageString(){return new ze("SigninState").create("toStorageString"),JSON.stringify({id:this.id,data:this.data,created:this.created,request_type:this.request_type,url_state:this.url_state,code_verifier:this.code_verifier,authority:this.authority,client_id:this.client_id,redirect_uri:this.redirect_uri,scope:this.scope,client_secret:this.client_secret,extraTokenParams:this.extraTokenParams,response_mode:this.response_mode,skipUserInfo:this.skipUserInfo})}static fromStorageString(t){ze.createStatic("SigninState","fromStorageString");const s=JSON.parse(t);return e.create(s)}},wt=class e{constructor(e){this.url=e.url,this.state=e.state}static async create({url:t,authority:s,client_id:n,redirect_uri:r,response_type:i,scope:o,state_data:a,response_mode:c,request_type:l,client_secret:u,nonce:_,url_state:d,resource:h,skipUserInfo:p,extraQueryParams:g,extraTokenParams:f,disablePKCE:w,dpopJkt:m,omitScopeWhenRequesting:y,...v}){if(!t)throw this._logger.error("create: No url passed"),new Error("url");if(!n)throw this._logger.error("create: No client_id passed"),new Error("client_id");if(!r)throw this._logger.error("create: No redirect_uri passed"),new Error("redirect_uri");if(!i)throw this._logger.error("create: No response_type passed"),new Error("response_type");if(!o)throw this._logger.error("create: No scope passed"),new Error("scope");if(!s)throw this._logger.error("create: No authority passed"),new Error("authority");const S=await ft.create({data:a,request_type:l,url_state:d,code_verifier:!w,client_id:n,authority:s,redirect_uri:r,response_mode:c,client_secret:u,scope:o,extraTokenParams:f,skipUserInfo:p}),k=new URL(t);k.searchParams.append("client_id",n),k.searchParams.append("redirect_uri",r),k.searchParams.append("response_type",i),y||k.searchParams.append("scope",o),_&&k.searchParams.append("nonce",_),m&&k.searchParams.append("dpop_jkt",m);let b=S.id;if(d&&(b=`${b}${tt}${d}`),k.searchParams.append("state",b),S.code_challenge&&(k.searchParams.append("code_challenge",S.code_challenge),k.searchParams.append("code_challenge_method","S256")),h){(Array.isArray(h)?h:[h]).forEach(e=>k.searchParams.append("resource",e))}for(const[e,E]of Object.entries({response_mode:c,...v,...g}))null!=E&&k.searchParams.append(e,E.toString());return new e({url:k.href,state:S})}};wt._logger=new ze("SigninRequest");var mt=wt,yt=class{constructor(e){if(this.access_token="",this.token_type="",this.profile={},this.state=e.get("state"),this.session_state=e.get("session_state"),this.state){const e=decodeURIComponent(this.state).split(tt);this.state=e[0],e.length>1&&(this.url_state=e.slice(1).join(tt))}this.error=e.get("error"),this.error_description=e.get("error_description"),this.error_uri=e.get("error_uri"),this.code=e.get("code")}get expires_in(){if(void 0!==this.expires_at)return this.expires_at-Xe.getEpochTime()}set expires_in(e){"string"==typeof e&&(e=Number(e)),void 0!==e&&e>=0&&(this.expires_at=Math.floor(e)+Xe.getEpochTime())}get isOpenId(){var e;return(null==(e=this.scope)?void 0:e.split(" ").includes("openid"))||!!this.id_token}},vt=class{constructor({url:e,state_data:t,id_token_hint:s,post_logout_redirect_uri:n,extraQueryParams:r,request_type:i,client_id:o,url_state:a}){if(this._logger=new ze("SignoutRequest"),!e)throw this._logger.error("ctor: No url passed"),new Error("url");const c=new URL(e);if(s&&c.searchParams.append("id_token_hint",s),o&&c.searchParams.append("client_id",o),n&&(c.searchParams.append("post_logout_redirect_uri",n),t||a)){this.state=new gt({data:t,request_type:i,url_state:a});let e=this.state.id;a&&(e=`${e}${tt}${a}`),c.searchParams.append("state",e)}for(const[l,u]of Object.entries({...r}))null!=u&&c.searchParams.append(l,u.toString());this.url=c.href}},St=class{constructor(e){if(this.state=e.get("state"),this.state){const e=decodeURIComponent(this.state).split(tt);this.state=e[0],e.length>1&&(this.url_state=e.slice(1).join(tt))}this.error=e.get("error"),this.error_description=e.get("error_description"),this.error_uri=e.get("error_uri")}},kt=["nbf","jti","auth_time","nonce","acr","amr","azp","at_hash"],bt=["sub","iss","aud","exp","iat"],Et=class{constructor(e){this._settings=e,this._logger=new ze("ClaimsService")}filterProtocolClaims(e){const t={...e};if(this._settings.filterProtocolClaims){let e;e=Array.isArray(this._settings.filterProtocolClaims)?this._settings.filterProtocolClaims:kt;for(const s of e)bt.includes(s)||delete t[s]}return t}mergeClaims(e,t){const s={...e};for(const[n,r]of Object.entries(t))if(s[n]!==r)if(Array.isArray(s[n])||Array.isArray(r))if("replace"==this._settings.mergeClaimsStrategy.array)s[n]=r;else{const e=Array.isArray(s[n])?s[n]:[s[n]];for(const t of Array.isArray(r)?r:[r])e.includes(t)||e.push(t);s[n]=e}else"object"==typeof s[n]&&"object"==typeof r?s[n]=this.mergeClaims(s[n],r):s[n]=r;return s}},Tt=class{constructor(e,t){this.keys=e,this.nonce=t}},xt=class{constructor(e,t){this._logger=new ze("OidcClient"),this.settings=e instanceof _t?e:new _t(e),this.metadataService=null!=t?t:new lt(this.settings),this._claimsService=new Et(this.settings),this._validator=new pt(this.settings,this.metadataService,this._claimsService),this._tokenClient=new ht(this.settings,this.metadataService)}async createSigninRequest({state:e,request:t,request_uri:s,request_type:n,id_token_hint:r,login_hint:i,skipUserInfo:o,nonce:a,url_state:c,response_type:l=this.settings.response_type,scope:u=this.settings.scope,redirect_uri:_=this.settings.redirect_uri,prompt:d=this.settings.prompt,display:h=this.settings.display,max_age:p=this.settings.max_age,ui_locales:g=this.settings.ui_locales,acr_values:f=this.settings.acr_values,resource:w=this.settings.resource,response_mode:m=this.settings.response_mode,extraQueryParams:y=this.settings.extraQueryParams,extraTokenParams:v=this.settings.extraTokenParams,dpopJkt:S,omitScopeWhenRequesting:k=this.settings.omitScopeWhenRequesting}){const b=this._logger.create("createSigninRequest");if("code"!==l)throw new Error("Only the Authorization Code flow (with PKCE) is supported");const E=await this.metadataService.getAuthorizationEndpoint();b.debug("Received authorization endpoint",E);const T=await mt.create({url:E,authority:this.settings.authority,client_id:this.settings.client_id,redirect_uri:_,response_type:l,scope:u,state_data:e,url_state:c,prompt:d,display:h,max_age:p,ui_locales:g,id_token_hint:r,login_hint:i,acr_values:f,dpopJkt:S,resource:w,request:t,request_uri:s,extraQueryParams:y,extraTokenParams:v,request_type:n,response_mode:m,client_secret:this.settings.client_secret,skipUserInfo:o,nonce:a,disablePKCE:this.settings.disablePKCE,omitScopeWhenRequesting:k});await this.clearStaleState();const x=T.state;return await this.settings.stateStore.set(x.id,x.toStorageString()),T}async readSigninResponseState(e,t=!1){const s=this._logger.create("readSigninResponseState"),n=new yt(et.readParams(e,this.settings.response_mode));if(!n.state)throw s.throw(new Error("No state in response")),null;const r=await this.settings.stateStore[t?"remove":"get"](n.state);if(!r)throw s.throw(new Error("No matching state found in storage")),null;return{state:await ft.fromStorageString(r),response:n}}async processSigninResponse(e,t,s=!0){const n=this._logger.create("processSigninResponse"),{state:r,response:i}=await this.readSigninResponseState(e,s);if(n.debug("received state from storage; validating response"),this.settings.dpop&&this.settings.dpop.store){const e=await this.getDpopProof(this.settings.dpop.store);t={...t,DPoP:e}}try{await this._validator.validateSigninResponse(i,r,t)}catch(o){if(!(o instanceof at&&this.settings.dpop))throw o;{const e=await this.getDpopProof(this.settings.dpop.store,o.nonce);t.DPoP=e,await this._validator.validateSigninResponse(i,r,t)}}return i}async getDpopProof(e,t){let s,n;return(await e.getAllKeys()).includes(this.settings.client_id)?(n=await e.get(this.settings.client_id),n.nonce!==t&&t&&(n.nonce=t,await e.set(this.settings.client_id,n))):(s=await Ge.generateDPoPKeys(),n=new Tt(s,t),await e.set(this.settings.client_id,n)),await Ge.generateDPoPProof({url:await this.metadataService.getTokenEndpoint(!1),httpMethod:"POST",keyPair:n.keys,nonce:n.nonce})}async processResourceOwnerPasswordCredentials({username:e,password:t,skipUserInfo:s=!1,extraTokenParams:n={}}){const r=await this._tokenClient.exchangeCredentials({username:e,password:t,...n}),i=new yt(new URLSearchParams);return Object.assign(i,r),await this._validator.validateCredentialsResponse(i,s),i}async useRefreshToken({state:e,redirect_uri:t,resource:s,timeoutInSeconds:n,extraHeaders:r,extraTokenParams:i}){var o;const a=this._logger.create("useRefreshToken");let c,l;if(void 0===this.settings.refreshTokenAllowedScope)c=e.scope;else{const t=this.settings.refreshTokenAllowedScope.split(" ");c=((null==(o=e.scope)?void 0:o.split(" "))||[]).filter(e=>t.includes(e)).join(" ")}if(this.settings.dpop&&this.settings.dpop.store){const e=await this.getDpopProof(this.settings.dpop.store);r={...r,DPoP:e}}try{l=await this._tokenClient.exchangeRefreshToken({refresh_token:e.refresh_token,scope:c,redirect_uri:t,resource:s,timeoutInSeconds:n,extraHeaders:r,...i})}catch(_){if(!(_ instanceof at&&this.settings.dpop))throw _;r.DPoP=await this.getDpopProof(this.settings.dpop.store,_.nonce),l=await this._tokenClient.exchangeRefreshToken({refresh_token:e.refresh_token,scope:c,redirect_uri:t,resource:s,timeoutInSeconds:n,extraHeaders:r,...i})}const u=new yt(new URLSearchParams);return Object.assign(u,l),a.debug("validating response",u),await this._validator.validateRefreshResponse(u,{...e,scope:c}),u}async createSignoutRequest({state:e,id_token_hint:t,client_id:s,request_type:n,url_state:r,post_logout_redirect_uri:i=this.settings.post_logout_redirect_uri,extraQueryParams:o=this.settings.extraQueryParams}={}){const a=this._logger.create("createSignoutRequest"),c=await this.metadataService.getEndSessionEndpoint();if(!c)throw a.throw(new Error("No end session endpoint")),null;a.debug("Received end session endpoint",c),s||!i||t||(s=this.settings.client_id);const l=new vt({url:c,id_token_hint:t,client_id:s,post_logout_redirect_uri:i,state_data:e,extraQueryParams:o,request_type:n,url_state:r});await this.clearStaleState();const u=l.state;return u&&(a.debug("Signout request has state to persist"),await this.settings.stateStore.set(u.id,u.toStorageString())),l}async readSignoutResponseState(e,t=!1){const s=this._logger.create("readSignoutResponseState"),n=new St(et.readParams(e,this.settings.response_mode));if(!n.state){if(s.debug("No state in response"),n.error)throw s.warn("Response was error:",n.error),new st(n);return{state:void 0,response:n}}const r=await this.settings.stateStore[t?"remove":"get"](n.state);if(!r)throw s.throw(new Error("No matching state found in storage")),null;return{state:await gt.fromStorageString(r),response:n}}async processSignoutResponse(e){const t=this._logger.create("processSignoutResponse"),{state:s,response:n}=await this.readSignoutResponseState(e,!0);return s?(t.debug("Received state from storage; validating response"),this._validator.validateSignoutResponse(n,s)):t.debug("No state from storage; skipping response validation"),n}clearStaleState(){return this._logger.create("clearStaleState"),gt.clearStaleState(this.settings.stateStore,this.settings.staleStateAgeInSeconds)}async revokeToken(e,t){return this._logger.create("revokeToken"),await this._tokenClient.revoke({token:e,token_type_hint:t})}},Pt=class{constructor(e){this._userManager=e,this._logger=new ze("SessionMonitor"),this._start=async e=>{const t=e.session_state;if(!t)return;const s=this._logger.create("_start");if(e.profile?(this._sub=e.profile.sub,s.debug("session_state",t,", sub",this._sub)):(this._sub=void 0,s.debug("session_state",t,", anonymous user")),this._checkSessionIFrame)this._checkSessionIFrame.start(t);else try{const e=await this._userManager.metadataService.getCheckSessionIframe();if(e){s.debug("initializing check session iframe");const n=this._userManager.settings.client_id,r=this._userManager.settings.checkSessionIntervalInSeconds,i=this._userManager.settings.stopCheckSessionOnError,o=new it(this._callback,n,e,r,i);await o.load(),this._checkSessionIFrame=o,o.start(t)}else s.warn("no check session iframe found in the metadata")}catch(n){s.error("Error from getCheckSessionIframe:",n instanceof Error?n.message:n)}},this._stop=()=>{const e=this._logger.create("_stop");if(this._sub=void 0,this._checkSessionIFrame&&this._checkSessionIFrame.stop(),this._userManager.settings.monitorAnonymousSession){const t=setInterval(async()=>{clearInterval(t);try{const e=await this._userManager.querySessionStatus();if(e){const t={session_state:e.session_state,profile:e.sub?{sub:e.sub}:null};this._start(t)}}catch(s){e.error("error from querySessionStatus",s instanceof Error?s.message:s)}},1e3)}},this._callback=async()=>{const e=this._logger.create("_callback");try{const t=await this._userManager.querySessionStatus();let s=!0;t&&this._checkSessionIFrame?t.sub===this._sub?(s=!1,this._checkSessionIFrame.start(t.session_state),e.debug("same sub still logged in at OP, session state has changed, restarting check session iframe; session_state",t.session_state),await this._userManager.events._raiseUserSessionChanged()):e.debug("different subject signed into OP",t.sub):e.debug("subject no longer signed into OP"),s?this._sub?await this._userManager.events._raiseUserSignedOut():await this._userManager.events._raiseUserSignedIn():e.debug("no change in session detected, no event to raise")}catch(t){this._sub&&(e.debug("Error calling queryCurrentSigninSession; raising signed out event",t),await this._userManager.events._raiseUserSignedOut())}},e||this._logger.throw(new Error("No user manager passed")),this._userManager.events.addUserLoaded(this._start),this._userManager.events.addUserUnloaded(this._stop),this._init().catch(e=>{this._logger.error(e)})}async _init(){this._logger.create("_init");const e=await this._userManager.getUser();if(e)this._start(e);else if(this._userManager.settings.monitorAnonymousSession){const e=await this._userManager.querySessionStatus();if(e){const t={session_state:e.session_state,profile:e.sub?{sub:e.sub}:null};this._start(t)}}}},Ct=class e{constructor(e){var t;this.id_token=e.id_token,this.session_state=null!=(t=e.session_state)?t:null,this.access_token=e.access_token,this.refresh_token=e.refresh_token,this.token_type=e.token_type,this.scope=e.scope,this.profile=e.profile,this.expires_at=e.expires_at,this.state=e.userState,this.url_state=e.url_state}get expires_in(){if(void 0!==this.expires_at)return this.expires_at-Xe.getEpochTime()}set expires_in(e){void 0!==e&&(this.expires_at=Math.floor(e)+Xe.getEpochTime())}get expired(){const e=this.expires_in;if(void 0!==e)return e<=0}get scopes(){var e,t;return null!=(t=null==(e=this.scope)?void 0:e.split(" "))?t:[]}toStorageString(){return new ze("User").create("toStorageString"),JSON.stringify({id_token:this.id_token,session_state:this.session_state,access_token:this.access_token,refresh_token:this.refresh_token,token_type:this.token_type,scope:this.scope,profile:this.profile,expires_at:this.expires_at})}static fromStorageString(t){return ze.createStatic("User","fromStorageString"),new e(JSON.parse(t))}},Rt="oidc-client",It=class{constructor(){this._abort=new Ye("Window navigation aborted"),this._disposeHandlers=new Set,this._window=null}async navigate(e){const t=this._logger.create("navigate");if(!this._window)throw new Error("Attempted to navigate on a disposed window");t.debug("setting URL in window"),this._window.location.replace(e.url);const{url:s,keepOpen:n}=await new Promise((s,n)=>{const r=r=>{var i;const o=r.data,a=null!=(i=e.scriptOrigin)?i:window.location.origin;if(r.origin===a&&(null==o?void 0:o.source)===Rt){try{const s=et.readParams(o.url,e.response_mode).get("state");if(s||t.warn("no state found in response url"),r.source!==this._window&&s!==e.state)return}catch{this._dispose(),n(new Error("Invalid response from window"))}s(o)}};window.addEventListener("message",r,!1),this._disposeHandlers.add(()=>window.removeEventListener("message",r,!1));const i=new BroadcastChannel(`oidc-client-popup-${e.state}`);i.addEventListener("message",r,!1),this._disposeHandlers.add(()=>i.close()),this._disposeHandlers.add(this._abort.addHandler(e=>{this._dispose(),n(e)}))});return t.debug("got response from window"),this._dispose(),n||this.close(),{url:s}}_dispose(){this._logger.create("_dispose");for(const e of this._disposeHandlers)e();this._disposeHandlers.clear()}static _notifyParent(e,t,s=!1,n=window.location.origin){const r={source:Rt,url:t,keepOpen:s},i=new ze("_notifyParent");if(e)i.debug("With parent. Using parent.postMessage."),e.postMessage(r,n);else{i.debug("No parent. Using BroadcastChannel.");const e=new URL(t).searchParams.get("state");if(!e)throw new Error("No parent and no state in URL. Can't complete notification.");const s=new BroadcastChannel(`oidc-client-popup-${e}`);s.postMessage(r),s.close()}}},Ut={location:!1,toolbar:!1,height:640,closePopupWindowAfterInSeconds:-1},At="_blank",Ot=60,Nt=2,qt=class extends _t{constructor(e){const{popup_redirect_uri:t=e.redirect_uri,popup_post_logout_redirect_uri:s=e.post_logout_redirect_uri,popupWindowFeatures:n=Ut,popupWindowTarget:r=At,redirectMethod:i="assign",redirectTarget:o="self",iframeNotifyParentOrigin:a=e.iframeNotifyParentOrigin,iframeScriptOrigin:c=e.iframeScriptOrigin,requestTimeoutInSeconds:l,silent_redirect_uri:u=e.redirect_uri,silentRequestTimeoutInSeconds:_,automaticSilentRenew:d=!0,validateSubOnSilentRenew:h=!0,includeIdTokenInSilentRenew:p=!1,monitorSession:g=!1,monitorAnonymousSession:f=!1,checkSessionIntervalInSeconds:w=Nt,query_status_response_type:m="code",stopCheckSessionOnError:y=!0,revokeTokenTypes:v=["access_token","refresh_token"],revokeTokensOnSignout:S=!1,includeIdTokenInSilentSignout:k=!1,accessTokenExpiringNotificationTimeInSeconds:b=Ot,userStore:E}=e;if(super(e),this.popup_redirect_uri=t,this.popup_post_logout_redirect_uri=s,this.popupWindowFeatures=n,this.popupWindowTarget=r,this.redirectMethod=i,this.redirectTarget=o,this.iframeNotifyParentOrigin=a,this.iframeScriptOrigin=c,this.silent_redirect_uri=u,this.silentRequestTimeoutInSeconds=_||l||10,this.automaticSilentRenew=d,this.validateSubOnSilentRenew=h,this.includeIdTokenInSilentRenew=p,this.monitorSession=g,this.monitorAnonymousSession=f,this.checkSessionIntervalInSeconds=w,this.stopCheckSessionOnError=y,this.query_status_response_type=m,this.revokeTokenTypes=v,this.revokeTokensOnSignout=S,this.includeIdTokenInSilentSignout=k,this.accessTokenExpiringNotificationTimeInSeconds=b,E)this.userStore=E;else{const e="undefined"!=typeof window?window.sessionStorage:new ot;this.userStore=new ut({store:e})}}},Ht=class e extends It{constructor({silentRequestTimeoutInSeconds:t=10}){super(),this._logger=new ze("IFrameWindow"),this._timeoutInSeconds=t,this._frame=e.createHiddenIframe(),this._window=this._frame.contentWindow}static createHiddenIframe(){const e=window.document.createElement("iframe");return e.style.visibility="hidden",e.style.position="fixed",e.style.left="-1000px",e.style.top="0",e.width="0",e.height="0",window.document.body.appendChild(e),e}async navigate(e){this._logger.debug("navigate: Using timeout of:",this._timeoutInSeconds);const t=setTimeout(()=>{this._abort.raise(new nt("IFrame timed out without a response"))},1e3*this._timeoutInSeconds);return this._disposeHandlers.add(()=>clearTimeout(t)),await super.navigate(e)}close(){var e;this._frame&&(this._frame.parentNode&&(this._frame.addEventListener("load",e=>{var t;const s=e.target;null==(t=s.parentNode)||t.removeChild(s),this._abort.raise(new Error("IFrame removed from DOM"))},!0),null==(e=this._frame.contentWindow)||e.location.replace("about:blank")),this._frame=null),this._window=null}static notifyParent(e,t){return super._notifyParent(window.parent,e,!1,t)}},Lt=class{constructor(e){this._settings=e,this._logger=new ze("IFrameNavigator")}async prepare({silentRequestTimeoutInSeconds:e=this._settings.silentRequestTimeoutInSeconds}){return new Ht({silentRequestTimeoutInSeconds:e})}async callback(e){this._logger.create("callback"),Ht.notifyParent(e,this._settings.iframeNotifyParentOrigin)}},$t=class extends It{constructor({popupWindowTarget:e=At,popupWindowFeatures:t={},popupSignal:s}){super(),this._logger=new ze("PopupWindow");const n=Ze.center({...Ut,...t});this._window=window.open(void 0,e,Ze.serialize(n)),s&&s.addEventListener("abort",()=>{var e;this._abort.raise(new Error(null!=(e=s.reason)?e:"Popup aborted"))}),t.closePopupWindowAfterInSeconds&&t.closePopupWindowAfterInSeconds>0&&setTimeout(()=>{this._window&&"boolean"==typeof this._window.closed&&!this._window.closed?this.close():this._abort.raise(new Error("Popup blocked by user"))},1e3*t.closePopupWindowAfterInSeconds)}async navigate(e){var t;null==(t=this._window)||t.focus();const s=setInterval(()=>{this._window&&!this._window.closed||(this._logger.debug("Popup closed by user or isolated by redirect"),n(),this._disposeHandlers.delete(n))},500),n=()=>clearInterval(s);return this._disposeHandlers.add(n),await super.navigate(e)}close(){this._window&&(this._window.closed||(this._window.close(),this._abort.raise(new Error("Popup closed")))),this._window=null}static notifyOpener(e,t){super._notifyParent(window.opener,e,t),t||window.opener||window.close()}},Mt=class{constructor(e){this._settings=e,this._logger=new ze("PopupNavigator")}async prepare({popupWindowFeatures:e=this._settings.popupWindowFeatures,popupWindowTarget:t=this._settings.popupWindowTarget,popupSignal:s}){return new $t({popupWindowFeatures:e,popupWindowTarget:t,popupSignal:s})}async callback(e,{keepOpen:t=!1}){this._logger.create("callback"),$t.notifyOpener(e,t)}},jt=class{constructor(e){this._settings=e,this._logger=new ze("RedirectNavigator")}async prepare({redirectMethod:e=this._settings.redirectMethod,redirectTarget:t=this._settings.redirectTarget}){var s;this._logger.create("prepare");let n=window.self;"top"===t&&(n=null!=(s=window.top)?s:window.self);const r=n.location[e].bind(n.location);let i;return{navigate:async e=>{this._logger.create("navigate");const t=new Promise((e,t)=>{i=t});return r(e.url),await t},close:()=>{this._logger.create("close"),null==i||i(new Error("Redirect aborted")),n.stop()}}}async callback(){}},Wt=class extends rt{constructor(e){super({expiringNotificationTimeInSeconds:e.accessTokenExpiringNotificationTimeInSeconds}),this._logger=new ze("UserManagerEvents"),this._userLoaded=new Ye("User loaded"),this._userUnloaded=new Ye("User unloaded"),this._silentRenewError=new Ye("Silent renew error"),this._userSignedIn=new Ye("User signed in"),this._userSignedOut=new Ye("User signed out"),this._userSessionChanged=new Ye("User session changed")}async load(e,t=!0){await super.load(e),t&&await this._userLoaded.raise(e)}async unload(){await super.unload(),await this._userUnloaded.raise()}addUserLoaded(e){return this._userLoaded.addHandler(e)}removeUserLoaded(e){return this._userLoaded.removeHandler(e)}addUserUnloaded(e){return this._userUnloaded.addHandler(e)}removeUserUnloaded(e){return this._userUnloaded.removeHandler(e)}addSilentRenewError(e){return this._silentRenewError.addHandler(e)}removeSilentRenewError(e){return this._silentRenewError.removeHandler(e)}async _raiseSilentRenewError(e){await this._silentRenewError.raise(e)}addUserSignedIn(e){return this._userSignedIn.addHandler(e)}removeUserSignedIn(e){this._userSignedIn.removeHandler(e)}async _raiseUserSignedIn(){await this._userSignedIn.raise()}addUserSignedOut(e){return this._userSignedOut.addHandler(e)}removeUserSignedOut(e){this._userSignedOut.removeHandler(e)}async _raiseUserSignedOut(){await this._userSignedOut.raise()}addUserSessionChanged(e){return this._userSessionChanged.addHandler(e)}removeUserSessionChanged(e){this._userSessionChanged.removeHandler(e)}async _raiseUserSessionChanged(){await this._userSessionChanged.raise()}},Dt=class{constructor(e){this._userManager=e,this._logger=new ze("SilentRenewService"),this._isStarted=!1,this._retryTimer=new Xe("Retry Silent Renew"),this._tokenExpiring=async()=>{const e=this._logger.create("_tokenExpiring");try{await this._userManager.signinSilent(),e.debug("silent token renewal successful")}catch(t){if(t instanceof nt)return e.warn("ErrorTimeout from signinSilent:",t,"retry in 5s"),void this._retryTimer.init(5);e.error("Error from signinSilent:",t),await this._userManager.events._raiseSilentRenewError(t)}}}async start(){const e=this._logger.create("start");if(!this._isStarted){this._isStarted=!0,this._userManager.events.addAccessTokenExpiring(this._tokenExpiring),this._retryTimer.addHandler(this._tokenExpiring);try{await this._userManager.getUser()}catch(t){e.error("getUser error",t)}}}stop(){this._isStarted&&(this._retryTimer.cancel(),this._retryTimer.removeHandler(this._tokenExpiring),this._userManager.events.removeAccessTokenExpiring(this._tokenExpiring),this._isStarted=!1)}},Ft=class{constructor(e){this.refresh_token=e.refresh_token,this.id_token=e.id_token,this.session_state=e.session_state,this.scope=e.scope,this.profile=e.profile,this.data=e.state}},Jt=class{constructor(e,t,s,n){this._logger=new ze("UserManager"),this.settings=new qt(e),this._client=new xt(e),this._redirectNavigator=null!=t?t:new jt(this.settings),this._popupNavigator=null!=s?s:new Mt(this.settings),this._iframeNavigator=null!=n?n:new Lt(this.settings),this._events=new Wt(this.settings),this._silentRenewService=new Dt(this),this.settings.automaticSilentRenew&&this.startSilentRenew(),this._sessionMonitor=null,this.settings.monitorSession&&(this._sessionMonitor=new Pt(this))}get events(){return this._events}get metadataService(){return this._client.metadataService}async getUser(e=!1){const t=this._logger.create("getUser"),s=await this._loadUser();return s?(t.info("user loaded"),await this._events.load(s,e),s):(t.info("user not found in storage"),null)}async removeUser(){const e=this._logger.create("removeUser");await this.storeUser(null),e.info("user removed from storage"),await this._events.unload()}async signinRedirect(e={}){var t;this._logger.create("signinRedirect");const{redirectMethod:s,...n}=e;let r;(null==(t=this.settings.dpop)?void 0:t.bind_authorization_code)&&(r=await this.generateDPoPJkt(this.settings.dpop));const i=await this._redirectNavigator.prepare({redirectMethod:s});await this._signinStart({request_type:"si:r",dpopJkt:r,...n},i)}async signinRedirectCallback(e=window.location.href){const t=this._logger.create("signinRedirectCallback"),s=await this._signinEnd(e);return s.profile&&s.profile.sub?t.info("success, signed in subject",s.profile.sub):t.info("no subject"),s}async signinResourceOwnerCredentials({username:e,password:t,skipUserInfo:s=!1}){const n=this._logger.create("signinResourceOwnerCredential"),r=await this._client.processResourceOwnerPasswordCredentials({username:e,password:t,skipUserInfo:s,extraTokenParams:this.settings.extraTokenParams});n.debug("got signin response");const i=await this._buildUser(r);return i.profile&&i.profile.sub?n.info("success, signed in subject",i.profile.sub):n.info("no subject"),i}async signinPopup(e={}){var t;const s=this._logger.create("signinPopup");let n;(null==(t=this.settings.dpop)?void 0:t.bind_authorization_code)&&(n=await this.generateDPoPJkt(this.settings.dpop));const{popupWindowFeatures:r,popupWindowTarget:i,popupSignal:o,...a}=e,c=this.settings.popup_redirect_uri;c||s.throw(new Error("No popup_redirect_uri configured"));const l=await this._popupNavigator.prepare({popupWindowFeatures:r,popupWindowTarget:i,popupSignal:o}),u=await this._signin({request_type:"si:p",redirect_uri:c,display:"popup",dpopJkt:n,...a},l);return u&&(u.profile&&u.profile.sub?s.info("success, signed in subject",u.profile.sub):s.info("no subject")),u}async signinPopupCallback(e=window.location.href,t=!1){const s=this._logger.create("signinPopupCallback");await this._popupNavigator.callback(e,{keepOpen:t}),s.info("success")}async signinSilent(e={}){var t,s;const n=this._logger.create("signinSilent"),{silentRequestTimeoutInSeconds:r,...i}=e;let o,a=await this._loadUser();if(null==a?void 0:a.refresh_token){n.debug("using refresh token");const e=new Ft(a);return await this._useRefreshToken({state:e,redirect_uri:i.redirect_uri,resource:i.resource,extraTokenParams:i.extraTokenParams,timeoutInSeconds:r})}(null==(t=this.settings.dpop)?void 0:t.bind_authorization_code)&&(o=await this.generateDPoPJkt(this.settings.dpop));const c=this.settings.silent_redirect_uri;let l;c||n.throw(new Error("No silent_redirect_uri configured")),a&&this.settings.validateSubOnSilentRenew&&(n.debug("subject prior to silent renew:",a.profile.sub),l=a.profile.sub);const u=await this._iframeNavigator.prepare({silentRequestTimeoutInSeconds:r});return a=await this._signin({request_type:"si:s",redirect_uri:c,prompt:"none",id_token_hint:this.settings.includeIdTokenInSilentRenew?null==a?void 0:a.id_token:void 0,dpopJkt:o,...i},u,l),a&&((null==(s=a.profile)?void 0:s.sub)?n.info("success, signed in subject",a.profile.sub):n.info("no subject")),a}async _useRefreshToken(e){const t=await this._client.useRefreshToken({timeoutInSeconds:this.settings.silentRequestTimeoutInSeconds,...e}),s=new Ct({...e.state,...t});return await this.storeUser(s),await this._events.load(s),s}async signinSilentCallback(e=window.location.href){const t=this._logger.create("signinSilentCallback");await this._iframeNavigator.callback(e),t.info("success")}async signinCallback(e=window.location.href){const{state:t}=await this._client.readSigninResponseState(e);switch(t.request_type){case"si:r":return await this.signinRedirectCallback(e);case"si:p":await this.signinPopupCallback(e);break;case"si:s":await this.signinSilentCallback(e);break;default:throw new Error("invalid response_type in state")}}async signoutCallback(e=window.location.href,t=!1){const{state:s}=await this._client.readSignoutResponseState(e);if(s)switch(s.request_type){case"so:r":return await this.signoutRedirectCallback(e);case"so:p":await this.signoutPopupCallback(e,t);break;case"so:s":await this.signoutSilentCallback(e);break;default:throw new Error("invalid response_type in state")}}async querySessionStatus(e={}){const t=this._logger.create("querySessionStatus"),{silentRequestTimeoutInSeconds:s,...n}=e,r=this.settings.silent_redirect_uri;r||t.throw(new Error("No silent_redirect_uri configured"));const i=await this._loadUser(),o=await this._iframeNavigator.prepare({silentRequestTimeoutInSeconds:s}),a=await this._signinStart({request_type:"si:s",redirect_uri:r,prompt:"none",id_token_hint:this.settings.includeIdTokenInSilentRenew?null==i?void 0:i.id_token:void 0,response_type:this.settings.query_status_response_type,scope:"openid",skipUserInfo:!0,...n},o);try{const e={},s=await this._client.processSigninResponse(a.url,e);return t.debug("got signin response"),s.session_state&&s.profile.sub?(t.info("success for subject",s.profile.sub),{session_state:s.session_state,sub:s.profile.sub}):(t.info("success, user not authenticated"),null)}catch(c){if(this.settings.monitorAnonymousSession&&c instanceof st)switch(c.error){case"login_required":case"consent_required":case"interaction_required":case"account_selection_required":return t.info("success for anonymous user"),{session_state:c.session_state}}throw c}}async _signin(e,t,s){const n=await this._signinStart(e,t);return await this._signinEnd(n.url,s)}async _signinStart(e,t){const s=this._logger.create("_signinStart");try{const n=await this._client.createSigninRequest(e);return s.debug("got signin request"),await t.navigate({url:n.url,state:n.state.id,response_mode:n.state.response_mode,scriptOrigin:this.settings.iframeScriptOrigin})}catch(n){throw s.debug("error after preparing navigator, closing navigator window"),t.close(),n}}async _signinEnd(e,t){const s=this._logger.create("_signinEnd"),n=await this._client.processSigninResponse(e,{});s.debug("got signin response");return await this._buildUser(n,t)}async _buildUser(e,t){const s=this._logger.create("_buildUser"),n=new Ct(e);if(t){if(t!==n.profile.sub)throw s.debug("current user does not match user returned from signin. sub from signin:",n.profile.sub),new st({...e,error:"login_required"});s.debug("current user matches user returned from signin")}return await this.storeUser(n),s.debug("user stored"),await this._events.load(n),n}async signoutRedirect(e={}){const t=this._logger.create("signoutRedirect"),{redirectMethod:s,...n}=e,r=await this._redirectNavigator.prepare({redirectMethod:s});await this._signoutStart({request_type:"so:r",post_logout_redirect_uri:this.settings.post_logout_redirect_uri,...n},r),t.info("success")}async signoutRedirectCallback(e=window.location.href){const t=this._logger.create("signoutRedirectCallback"),s=await this._signoutEnd(e);return t.info("success"),s}async signoutPopup(e={}){const t=this._logger.create("signoutPopup"),{popupWindowFeatures:s,popupWindowTarget:n,popupSignal:r,...i}=e,o=this.settings.popup_post_logout_redirect_uri,a=await this._popupNavigator.prepare({popupWindowFeatures:s,popupWindowTarget:n,popupSignal:r});await this._signout({request_type:"so:p",post_logout_redirect_uri:o,state:null==o?void 0:{},...i},a),t.info("success")}async signoutPopupCallback(e=window.location.href,t=!1){const s=this._logger.create("signoutPopupCallback");await this._popupNavigator.callback(e,{keepOpen:t}),s.info("success")}async _signout(e,t){const s=await this._signoutStart(e,t);return await this._signoutEnd(s.url)}async _signoutStart(e={},t){var s;const n=this._logger.create("_signoutStart");try{const r=await this._loadUser();n.debug("loaded current user from storage"),this.settings.revokeTokensOnSignout&&await this._revokeInternal(r);const i=e.id_token_hint||r&&r.id_token;i&&(n.debug("setting id_token_hint in signout request"),e.id_token_hint=i),await this.removeUser(),n.debug("user removed, creating signout request");const o=await this._client.createSignoutRequest(e);return n.debug("got signout request"),await t.navigate({url:o.url,state:null==(s=o.state)?void 0:s.id,scriptOrigin:this.settings.iframeScriptOrigin})}catch(r){throw n.debug("error after preparing navigator, closing navigator window"),t.close(),r}}async _signoutEnd(e){const t=this._logger.create("_signoutEnd"),s=await this._client.processSignoutResponse(e);return t.debug("got signout response"),s}async signoutSilent(e={}){var t;const s=this._logger.create("signoutSilent"),{silentRequestTimeoutInSeconds:n,...r}=e,i=this.settings.includeIdTokenInSilentSignout?null==(t=await this._loadUser())?void 0:t.id_token:void 0,o=this.settings.popup_post_logout_redirect_uri,a=await this._iframeNavigator.prepare({silentRequestTimeoutInSeconds:n});await this._signout({request_type:"so:s",post_logout_redirect_uri:o,id_token_hint:i,...r},a),s.info("success")}async signoutSilentCallback(e=window.location.href){const t=this._logger.create("signoutSilentCallback");await this._iframeNavigator.callback(e),t.info("success")}async revokeTokens(e){const t=await this._loadUser();await this._revokeInternal(t,e)}async _revokeInternal(e,t=this.settings.revokeTokenTypes){const s=this._logger.create("_revokeInternal");if(!e)return;const n=t.filter(t=>"string"==typeof e[t]);if(n.length){for(const t of n)await this._client.revokeToken(e[t],t),s.info(`${t} revoked successfully`),"access_token"!==t&&(e[t]=null);await this.storeUser(e),s.debug("user stored"),await this._events.load(e)}else s.debug("no need to revoke due to no token(s)")}startSilentRenew(){this._logger.create("startSilentRenew"),this._silentRenewService.start()}stopSilentRenew(){this._silentRenewService.stop()}get _userStoreKey(){return`user:${this.settings.authority}:${this.settings.client_id}`}async _loadUser(){const e=this._logger.create("_loadUser"),t=await this.settings.userStore.get(this._userStoreKey);return t?(e.debug("user storageString loaded"),Ct.fromStorageString(t)):(e.debug("no user storageString"),null)}async storeUser(e){const t=this._logger.create("storeUser");if(e){t.debug("storing user");const s=e.toStorageString();await this.settings.userStore.set(this._userStoreKey,s)}else this._logger.debug("removing user"),await this.settings.userStore.remove(this._userStoreKey),this.settings.dpop&&await this.settings.dpop.store.remove(this.settings.client_id)}async clearStaleState(){await this._client.clearStaleState()}async dpopProof(e,t,s,n){var r,i;const o=await(null==(i=null==(r=this.settings.dpop)?void 0:r.store)?void 0:i.get(this.settings.client_id));if(o)return await Ge.generateDPoPProof({url:e,accessToken:null==t?void 0:t.access_token,httpMethod:s,keyPair:o.keys,nonce:n})}async generateDPoPJkt(e){let t=await e.store.get(this.settings.client_id);if(!t){const s=await Ge.generateDPoPKeys();t=new Tt(s),await e.store.set(this.settings.client_id,t)}return await Ge.generateDPoPJkt(t.keys)}};const Kt="INITIALISED",zt="USER_LOADED",Bt="USER_UNLOADED",Vt="NAVIGATOR_INIT",Qt="NAVIGATOR_CLOSE",Gt="ERROR",Yt=e=>({type:Kt,payload:{user:e}}),Zt=e=>({type:zt,payload:{user:e}}),Xt=()=>({type:Bt}),es=e=>({type:Vt,payload:{method:e}}),ts=()=>({type:Qt}),ss=e=>({type:Gt,payload:{error:e}}),ns=(e,{type:t,payload:s=null})=>{switch(t){case Kt:case zt:return{...e,user:s.user,isLoading:!1,isAuthenticated:!!s.user&&!s.user.expired,error:null};case Bt:return{...e,user:null,isAuthenticated:!1};case Vt:return{...e,isLoading:!0,activeNavigator:s.method};case Qt:return{...e,isLoading:!1,activeNavigator:null};case Gt:return{...e,isLoading:!1,error:s.error};default:return{...e,isLoading:!1,error:new Error(`unknown type ${t}`)}}};const rs=["removeUser","clearStaleState","querySessionStatus","revokeTokens","startSilentRenew","stopSilentRenew","getUser"],is=["signinSilent","signinRedirect","signinResourceOwnerCredentials","signoutRedirect","signoutSilent"],os=function(e){function t(e){var s,n;return this.getChildContext||(s=new Set,(n={})[t.__c]=this,this.getChildContext=function(){return n},this.componentWillUnmount=function(){s=null},this.shouldComponentUpdate=function(e){this.props.value!=e.value&&s.forEach(function(e){e.__e=!0,E(e)})},this.sub=function(e){s.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){s&&s.delete(e),t&&t.call(e)}}),e.children}return t.__c="__cC"+_++,t.__=e,t.Provider=t.__l=(t.Consumer=function(e,t){return e.children(t)}).contextType=t,t}(null);os.displayName="OidcAuthContext";const as="/signin",cs="/signin-oidc",ls="/signout",us="/signout-oidc";const _s={isLoading:!0,isAuthenticated:!1,user:null,error:null,activeNavigator:null},ds=typeof window>"u"?null:Jt,hs=(gs="Login failed",e=>e instanceof Error?e:new Error(gs)),ps=({children:e,userManagerProp:t,integrationLoginComponent:s=null,integrationLogoutComponent:n,clientSettings:r={},enabled:i=!0})=>{const[o]=function(e){return z=1,se(he,e)}(()=>t||(ds?new ds(r):r)),[a,c]=se(ns,_s),l=re(!1),u=ie(()=>Object.assign({settings:o.settings,events:o.events},Object.fromEntries(rs.map(e=>[e,o[e].bind(o)])),Object.fromEntries(is.map(e=>[e,async t=>{c(es(e));try{return await o[e](t)}catch(s){return c(ss(s)),null}finally{c(ts())}}]))),[o]);"function"!=typeof s&&(s=function(){if(!window.euroland)throw new Error("Integration window.euroland object not found. Check out whether integration.js is included.");let e=(document.querySelector("base")||{}).href;return/\/$/.test(e)&&(e=e.replace(/\/$/,"")),window.euroland.createComponent("LoginComponent",{tag:"ai-search-signin-component",url:`${e}${as}${window.location.search}`,dimensions:{width:"850px",height:"600px"},template:{name:"popup"},props:{returnUrl:{type:"string",required:!1,default:""},onLoginSuccess:{type:"function",required:!0},onLoginFail:{type:"function",required:!0}}})}()),"function"!=typeof n&&(n=function(){if(!window.euroland)throw new Error("Integration window.euroland object not found. Check out whether integration.js is included.");let e=(document.querySelector("base")||{}).href;return/\/$/.test(e)&&(e=e.replace(/\/$/,"")),window.euroland.createComponent("LogoutComponent",{tag:"ai-search-signout-component",url:`${e}${ls}${window.location.search}`,dimensions:{width:"700px",height:"600px"},template:{name:"popup"},props:{onLogoutSuccess:{type:"function",required:!0},onLogoutFail:{type:"function",required:!0}}})}()),ne(()=>{!i||!o||l.current||(l.current=!0,(async()=>{let e=null;try{((e=window.location)=>{let t=new URLSearchParams(e.search),s=new URLSearchParams;for(const[n,r]of t)s.append(n.toLowerCase(),r);if((s.get("code")||s.get("error"))&&s.get("state"))return!0;t=new URLSearchParams(e.hash.replace("#","?")),s=new URLSearchParams;for(const[n,r]of t)s.append(n.toLowerCase(),r);return!(!s.get("code")&&!s.get("error")||!s.get("state"))})()&&(e=await o.signinCallback()||null),e=e||await o.getUser(),!e||e.expired,c(Yt(e))}catch(t){t&&"login_required"!==t.error&&c(ss(hs(t)))}})())},[o]),ne(()=>{if(!o)return;const e=e=>{ze.info("OidcAuthProvider","user loaded",e),c(Zt(e)),o.getUser().then(()=>{ze.info("OidcAuthProvider","getUser() loaded user after userLoaded event fired")})},t=()=>{ze.info("OidcAuthProvider","user unloaded"),c(Xt())},s=e=>{c(ss(e))},n=()=>{ze.info("OidcAuthProvider","token expiring")},r=()=>{ze.info("OidcAuthProvider","token expired")},i=()=>{ze.info("OidcAuthProvider","user logged out of the token server")},a=()=>{ze.info("OidcAuthProvider","user logged in to the token server")};return o.events.addUserLoaded(e),o.events.addUserUnloaded(t),o.events.addSilentRenewError(s),o.events.addAccessTokenExpiring(n),o.events.addAccessTokenExpired(r),o.events.addUserSignedIn(a),o.events.addUserSignedOut(i),()=>{o.events.removeUserLoaded(e),o.events.removeUserUnloaded(t),o.events.removeSilentRenewError(s),o.events.removeAccessTokenExpiring(n),o.events.removeAccessTokenExpired(r),o.events.removeUserSignedIn(a),o.events.removeUserSignedOut(i)}},[o]);const _=oe((e={})=>{(async()=>{try{await o.signinRedirect({state:e})}catch(t){c(ss(t))}})()},[o]),d=oe(()=>{(async()=>{try{await o.signoutRedirect()}catch(e){c(ss(e))}})()},[o]),h=oe(()=>{(async()=>{try{await o.signinSilent()}catch(e){c(ss(e))}})()},[o]),p=oe(e=>{e&&(async()=>{await o.storeUser(e),c(Zt(e))})()},[o]),g=oe(async()=>{const e=await function({LoginComponent:e}){e||Promise.reject("Integration LoginComponent is required");const t=new Promise((t,s)=>{const n=window.top!==window.self,r=e({onLoginSuccess:t,onLoginFail:e=>{s(e)}});if(window.euroland&&r.event.on(window.euroland.EVENT.ERROR,e=>{s(e)}),n)r.renderTo(window.parent,window.xprops.layout.middle,"popup");else{let e=document.getElementById("middleLayout");e||(e=document.createElement("div"),e.id="middleLayout",document.body.appendChild(e)),r.renderTo(window.parent,"#middleLayout","popup")}}),s=e.canRenderTo(window.parent);return Promise.all([s,t]).then(e=>e[1])}({LoginComponent:s});p(new Ct({...e}))},[s,p]),f=oe(async()=>{const e=await function({LogoutComponent:e}){e||Promise.reject("Integration LogoutComponent is required");const t=new Promise((t,s)=>{var n,r;const i=window.top!==window.self;let o=!1,a=null;const c=e({onLogoutSuccess:()=>o=!0,onLogoutFail:e=>a=e});if(window.euroland&&(c.event.on(window.euroland.EVENT.CLOSE,()=>{t(o)}),c.event.on(window.euroland.EVENT.ERROR,e=>{s({internalError:a,windowError:e})})),i)c.renderTo(window.parent,null==(r=null==(n=window.xprops)?void 0:n.layout)?void 0:r.middle,"popup");else{let e=document.getElementById("middleLayout");e||(e=document.createElement("div"),e.id="middleLayout",document.body.appendChild(e)),c.renderTo(window.parent,"#middleLayout","popup")}}),s=e.canRenderTo(window.parent);return Promise.all([s,t]).then(e=>e[1])}({LogoutComponent:n});try{if(e)return;await o.querySessionStatus()}catch(t){if("login_required"===t.error)return;throw t}finally{await o.clearStaleState(),await o.removeUser()}},[o]),w=oe(async()=>{try{return await o.signinSilent()}catch(e){return c(ss(e)),Promise.reject(e)}},[o]);return W(os.Provider,{value:{...a,...u,login:g,logout:f,signinRedirect:_,signoutRedirect:d,signinSilent:w,renewToken:h},children:e})};var gs;function fs(){const e=function(e){var t=F.context[e.__c],s=te(D++,9);return s.c=e,t?(null==s.__&&(s.__=!0,t.sub(F)),t.props.value):e.__}(os);if(!e)throw new Error("OidcAuthProvider context is undefined, please verify you are calling useAuth() as child of a <OidcAuthProvider> component.");return e}const ws=()=>{const e=fs();if(e.isLoading)return null;if(e.isAuthenticated){const t=window.xprops;"function"==typeof t.onLoginSuccess&&e.user&&t.onLoginSuccess(e.user),"function"==typeof t.close&&t.close()}else e.signinRedirect();return null},ms=window.self!==window.top,ys=()=>{const e=fs(),t=window.xprops;return ms||"xprops"in window?(e.isAuthenticated&&t&&("function"==typeof t.onLoginSuccess&&e.user&&t.onLoginSuccess(e.user),"function"==typeof t.close&&t.close()),null):W(v,{children:W("div",{className:"p-4 text-center",children:[W("h2",{className:"text-xl font-bold mb-2",children:"Registration Successful!"}),W("p",{className:"mb-4",children:"Your account has been verified successfully."}),W("p",{children:"Please close this window and return to the application where you started the login process."}),W("p",{className:"mt-4 text-sm text-gray-600",children:"If you're not sure where to go, try checking your recently opened tabs or applications."})]})})},vs=()=>{const e=fs();if(e.isLoading)return null;if(e.isAuthenticated)e.signoutRedirect();else{const e=window.xprops;"function"==typeof e.onLogoutSuccess&&e.onLogoutSuccess(),"function"==typeof e.close&&e.close()}return null},Ss=()=>{const e=fs(),t=window.xprops;return!e.isAuthenticated&&t&&("function"==typeof t.onLogoutSuccess&&t.onLogoutSuccess(),"function"==typeof t.close&&t.close()),null};function ks(...e){return function(e){const t=[];if(0===e.length)return"";if("string"!=typeof(e=e.filter(e=>""!==e))[0])throw new TypeError("Url must be a string. Received "+e[0]);e[0].match(/^[^/:]+:\/*$/)&&e.length>1&&(e[0]=e.shift()+e[0]),"/"===e[0]&&e.length>1&&(e[0]=e.shift()+e[0]),e[0].match(/^file:\/\/\//)?e[0]=e[0].replace(/^([^/:]+):\/*/,"$1:///"):e[0].match(/^\[.*:.*\]/)||(e[0]=e[0].replace(/^([^/:]+):\/*/,"$1://"));for(let o=0;o<e.length;o++){let s=e[o];if("string"!=typeof s)throw new TypeError("Url must be a string. Received "+s);o>0&&(s=s.replace(/^[/]+/,"")),s=o<e.length-1?s.replace(/[/]+$/,""):s.replace(/[/]+$/,"/"),""!==s&&t.push(s)}let s="";for(let o=0;o<t.length;o++){const e=t[o];if(0===o){s+=e;continue}const n=t[o-1];n&&n.endsWith("?")||n.endsWith("#")?s+=e:s+="/"+e}s=s.replace(/\/(\?|&|#[^!])/g,"$1");const[n,r]=s.split("#"),i=n.split(/(?:\?|&)+/).filter(Boolean);return s=i.shift()+(i.length>0?"?":"")+i.join("&")+(r&&r.length>0?"#"+r:""),s}(Array.from(Array.isArray(e[0])?e[0]:e))}function bs({baseUrl:e}){const t=t=>{const s=e.replace(/\/$/,""),n=window.location.pathname,r=ks(s,t);return n.replace(/\/$/,"")===r.replace(/\/$/,"")};return t(as)?W(ws,{}):t(ls)?W(vs,{}):t(cs)?W(ys,{}):t(us)?W(Ss,{}):null}const Es=(document.querySelector("base[href]")?.href||"").substring(location.origin.length)||"/",Ts="authState",xs=["login","logout",Ts];function Ps(){const e=fs();return((e={})=>{const t=fs(),s=re(!1),n=re(e);n.current=e,ne(()=>{if(s.current)return;const e=async()=>{var e,s;await t.clearStaleState(),await t.removeUser(),null==(s=(e=n.current).onSigninSilentFailed)||s.call(e)};(async()=>{try{t.isLoading||(s.current=!0,await t.signinSilent()||e())}catch{e()}})()},[t])})(),ne(()=>{const t=window.EurolandAppContext;if(!t)return;console.log("auth changed",e);const s={isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,user:e.user?.profile||null,accessToken:e.user?.access_token||null};return xs.forEach(n=>{t.registerCommandHandler(n,()=>n===Ts?s:e[n])}),t.emit("authChanged",s),()=>{xs.forEach(e=>{t.unregisterCommandHandler(e)})}},[e]),W(bs,{baseUrl:Es})}const Cs=new Promise(e=>{let t=(document.querySelector("base")||{}).href;/\/$/.test(t)&&(t=t.replace(/\/$/,""));const s=new URLSearchParams(window.location.search).get("companyCode");e({authority:"https://dev.vn.euroland.com/auth/realms/irservices",client_id:"share-graph-pro",redirect_uri:`${t}${cs}/?companyCode=${s}`,silent_redirect_uri:`${t}${cs}/?companyCode=${s}`,post_logout_redirect_uri:`${t}${us}/?companyCode=${s}`,response_type:"code",loadUserInfo:!0,scope:"openid profile",automaticSilentRenew:!0})}),Rs=async e=>{const t=await Cs,s=new Jt(t);M(W(ps,{userManagerProp:s,clientSettings:t,children:W(Ps,{})}),e)};class Is extends HTMLElement{container=null;constructor(){super(),this.attachShadow({mode:"open"});const e=document.createElement("style");e.textContent="",this.container=document.createElement("div"),this.container.id="preact-root",this.shadowRoot.appendChild(e),this.shadowRoot.appendChild(this.container)}connectedCallback(){this.container&&Rs(this.container)}disconnectedCallback(){this.container&&M(null,this.container)}}customElements.define("euroland-auth",Is)});
//# sourceMappingURL=auth-widget.umd.js.map
