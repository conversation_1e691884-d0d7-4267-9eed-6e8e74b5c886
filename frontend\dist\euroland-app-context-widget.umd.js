!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).EurolandAppContext=t()}(this,function(){"use strict";const e="COMMAND_REGISTERED",t="COMMAND_UNREGISTERED",n="STATE_CHANGED",s="STATE_REMOVED",o="COMMAND_EXECUTED";return new class{_commandHandlers=new Map;_state=new Map;_eventListeners=new Map;constructor(){window.EurolandAppContext=this}command(e,t){const n=this._commandHandlers.get(e);if(n){const s=n(t);return this.emit(o,{command:e,payload:t,result:s}),s}console.warn(`EurolandAppContext: Command '${e}' not found`)}registerCommandHandler(t,n){this._commandHandlers.set(t,n),this.emit(e,{command:t}),console.log(`EurolandAppContext: Command '${t}' registered`)}unregisterCommandHandler(e){const n=this._commandHandlers.delete(e);return n&&(this.emit(t,{command:e}),console.log(`EurolandAppContext: Command '${e}' unregistered`)),n}setState(e,t){const s=this._state.get(e);this._state.set(e,t),this.emit(n,{key:e,value:t,oldValue:s}),console.log(`EurolandAppContext: State '${e}' updated`,{oldValue:s,newValue:t})}getState(e){return this._state.get(e)}removeState(e){const t=this._state.delete(e);return t&&(this.emit(s,{key:e}),console.log(`EurolandAppContext: State '${e}' removed`)),t}on(e,t){this._eventListeners.has(e)||this._eventListeners.set(e,new Set),this._eventListeners.get(e).add(t)}off(e,t){const n=this._eventListeners.get(e);n&&(n.delete(t),0===n.size&&this._eventListeners.delete(e))}emit(e,t){const n=this._eventListeners.get(e);n&&n.forEach(n=>{try{n(t)}catch(s){console.error(`EurolandAppContext: Error in event listener for '${e}':`,s)}})}getAllCommands(){return Array.from(this._commandHandlers.keys())}hasCommand(e){return this._commandHandlers.has(e)}getAllState(){return Object.fromEntries(this._state)}getEventListeners(){const e={};return this._eventListeners.forEach((t,n)=>{e[n]=t.size}),e}clear(){this._commandHandlers.clear(),this._state.clear(),this._eventListeners.clear(),console.log("EurolandAppContext: All data cleared")}}});
//# sourceMappingURL=euroland-app-context-widget.umd.js.map
